import { Document } from 'mongoose';
export interface ITradeConfirmation extends Document {
    confirmationId: string;
    tradeId: string;
    discordId: string;
    guildId: string;
    confirmationType: 'TRADE_ACCEPTANCE' | 'ITEM_RECEIVED' | 'TRADE_COMPLETION' | 'DISPUTE_ACKNOWLEDGMENT';
    confirmed: boolean;
    confirmedAt: Date;
    notes?: string;
    ipAddress?: string;
    userAgent?: string;
    previousConfirmationId?: string;
    isRevoked: boolean;
    revokedAt?: Date;
    revokeReason?: string;
}
declare const _default: import("mongoose").Model<ITradeConfirmation, {}, {}, {}, Document<unknown, {}, ITradeConfirmation, {}> & ITradeConfirmation & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=TradeConfirmation.d.ts.map