import { Document } from 'mongoose';
export interface ITrade extends Document {
    tradeId: string;
    sellerId: string;
    buyerId: string;
    guildId: string;
    amount: number;
    itemDescription: string;
    notes?: string;
    state: 'PROPOSED' | 'ACCEPTED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'EXPIRED' | 'DISPUTED';
    initiatedBy: 'SELLER' | 'BUYER';
    createdAt: Date;
    acceptedAt?: Date;
    expiresAt: Date;
    completedAt?: Date;
    escrowLocked: boolean;
    escrowAmount: number;
    sellerConfirmed: boolean;
    buyerConfirmed: boolean;
    sellerConfirmedAt?: Date;
    buyerConfirmedAt?: Date;
    disputeId?: string;
    disputedBy?: string;
    disputedAt?: Date;
    disputeReason?: string;
    lastWarningAt?: Date;
    warningsSent: number;
    extensionGranted: boolean;
}
declare const _default: import("mongoose").Model<ITrade, {}, {}, {}, Document<unknown, {}, ITrade, {}> & ITrade & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Trade.d.ts.map