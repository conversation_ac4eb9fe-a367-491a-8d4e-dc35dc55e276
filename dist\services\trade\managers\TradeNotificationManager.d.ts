/**
 * Trade Notification Manager
 * Handles Discord notifications for trade events
 */
import { Client } from 'discord.js';
import { BaseService } from '../../base/BaseService';
import { ITrade } from '../../../models';
/**
 * Trade Notification Manager Class
 */
export declare class TradeNotificationManager extends BaseService {
    constructor(app: any);
    /**
     * Initialize the notification manager
     */
    initialize(): Promise<void>;
    /**
     * Send trade proposal notification
     */
    sendTradeProposal(trade: ITrade, client: Client): Promise<void>;
    /**
     * Send trade accepted notification
     */
    sendTradeAccepted(trade: ITrade, client: Client): Promise<void>;
    /**
     * Send trade completed notification
     */
    sendTradeCompleted(trade: ITrade, client: Client): Promise<void>;
    /**
     * Send partial confirmation notification
     */
    sendPartialConfirmation(trade: ITrade, confirmingUserId: string, client: Client): Promise<void>;
    /**
     * Send trade cancelled notification
     */
    sendTradeCancelled(trade: ITrade, cancellingUserId: string, reason: string | undefined, client: Client): Promise<void>;
    /**
     * Send trade expired notification
     */
    sendTradeExpired(trade: ITrade, client: Client): Promise<void>;
    /**
     * Send trade warning notification
     */
    sendTradeWarning(trade: ITrade, hoursRemaining: number, client: Client): Promise<void>;
    /**
     * Send dispute initiated notification
     */
    sendDisputeInitiated(trade: ITrade, disputingUserId: string, client: Client): Promise<void>;
    /**
     * Send dispute resolved notification
     */
    sendDisputeResolved(trade: ITrade, resolution: string, client: Client): Promise<void>;
}
//# sourceMappingURL=TradeNotificationManager.d.ts.map