/**
 * Trade Monitor Command
 * Real-time monitoring and management for the trade system
 */
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { TradeService } from '../../services/trade/TradeService';
import { DisputeService } from '../../services/trade/DisputeService';
import { TradeBackgroundService } from '../../services/trade/TradeBackgroundService';
/**
 * Trade Monitor command implementation
 */
export declare class TradeMonitorCommand extends BaseCommand {
    private tradeService;
    private disputeService;
    private backgroundService;
    constructor();
    /**
     * Set the trade services (dependency injection)
     */
    setTradeServices(tradeService: TradeService, disputeService: DisputeService, backgroundService: TradeBackgroundService): void;
    /**
     * Customize the command builder with subcommands
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the trade monitor command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
    /**
     * Handle active trades monitoring
     */
    private handleActive;
    /**
     * Handle stuck trades detection
     */
    private handleStuck;
    /**
     * Handle escrow consistency check
     */
    private handleEscrow;
    /**
     * Handle manual cleanup trigger
     */
    private handleCleanup;
    /**
     * Handle comprehensive health check
     */
    private handleHealth;
    /**
     * Handle user analysis
     */
    private handleUser;
    /**
     * Handle alerts view
     */
    private handleAlerts;
    /**
     * Handle performance metrics
     */
    private handlePerformance;
}
//# sourceMappingURL=TradeMonitorCommand.d.ts.map