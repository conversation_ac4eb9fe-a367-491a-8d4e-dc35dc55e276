"use strict";
/**
 * Interaction Create Event Handler
 * Handles Discord interaction events (commands and buttons)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionCreateEventHandler = void 0;
const base_1 = require("./base");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
/**
 * Interaction create event handler
 */
class InteractionCreateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'interactionCreate');
        this.name = 'interactionCreate';
    }
    /**
     * Execute interaction create event
     */
    async execute(interaction) {
        try {
            if (interaction.isChatInputCommand()) {
                await this.handleChatInputCommand(interaction);
            }
            else if (interaction.isButton()) {
                await this.handleButtonInteraction(interaction);
            }
        }
        catch (error) {
            this.handleError(error, {
                interactionType: interaction.type,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
        }
    }
    /**
     * Handle chat input command interactions
     */
    async handleChatInputCommand(interaction) {
        const command = this.app.client.commands.get(interaction.commandName);
        if (!command) {
            this.logger.warn(`[InteractionCreate] Unknown command: ${interaction.commandName}`);
            return;
        }
        try {
            this.logExecution(`Executing command: ${interaction.commandName}`, {
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
                channelId: interaction.channel?.id,
            });
            // Create command context
            const context = {
                interaction,
                client: this.app.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            await command.execute(interaction);
        }
        catch (error) {
            await (0, errorHandler_1.handleCommandError)(interaction, error);
        }
    }
    /**
     * Handle button interactions
     */
    async handleButtonInteraction(interaction) {
        try {
            const { customId } = interaction;
            this.logExecution(`Handling button interaction: ${customId}`, {
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
                channelId: interaction.channel?.id,
            });
            // Create button context
            const context = {
                interaction,
                client: this.app.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            // Handle different button types
            if (customId === 'quick_balance') {
                await this.handleQuickAction(interaction, 'balance');
            }
            else if (customId === 'quick_leaderboard') {
                await this.handleQuickAction(interaction, 'leaderboard');
            }
            else if (customId === 'quick_roles') {
                await this.handleQuickAction(interaction, 'roles');
            }
            else if (customId.startsWith('buy_role_')) {
                await this.handleRoleAchievementInfo(interaction, customId);
            }
            else if (customId === 'announce_confirm') {
                await this.handleAnnouncementConfirm(interaction);
            }
            else if (customId === 'announce_cancel') {
                await this.handleAnnouncementCancel(interaction);
            }
            else if (customId.startsWith('help_')) {
                await this.handleHelpButton(interaction, customId);
            }
            else if (customId.startsWith('trade_')) {
                await this.handleTradeButton(interaction, customId);
            }
            else {
                await interaction.reply({
                    content: 'This button interaction is not yet implemented.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            await (0, errorHandler_1.handleButtonError)(interaction, error);
        }
    }
    /**
     * Handle quick action buttons
     */
    async handleQuickAction(interaction, commandName) {
        const command = this.app.client.commands.get(commandName);
        if (command) {
            await command.execute(interaction);
        }
        else {
            await interaction.reply({
                content: `Command "${commandName}" not found.`,
                ephemeral: true
            });
        }
    }
    /**
     * Handle role achievement info buttons
     */
    async handleRoleAchievementInfo(interaction, customId) {
        const roleId = customId.replace('buy_role_', '');
        await interaction.reply({
            content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
            ephemeral: true
        });
    }
    /**
     * Handle announcement confirmation
     */
    async handleAnnouncementConfirm(interaction) {
        const pendingAnnouncements = global.pendingAnnouncements;
        if (!pendingAnnouncements) {
            await interaction.reply({
                content: 'No pending announcements found. Please try the command again.',
                ephemeral: true
            });
            return;
        }
        const originalInteractionId = interaction.message?.interaction?.id;
        const announcementData = pendingAnnouncements.get(originalInteractionId);
        if (!announcementData) {
            await interaction.reply({
                content: 'Announcement data not found or expired. Please try the command again.',
                ephemeral: true
            });
            return;
        }
        // Clean up the pending announcement
        pendingAnnouncements.delete(originalInteractionId);
        // Process the announcement
        await interaction.deferUpdate();
        const announceModule = require('../commands/announce');
        await announceModule.processAnnouncement(interaction, announcementData);
    }
    /**
     * Handle announcement cancellation
     */
    async handleAnnouncementCancel(interaction) {
        const pendingAnnouncements = global.pendingAnnouncements;
        const originalInteractionId = interaction.message?.interaction?.id;
        if (pendingAnnouncements && originalInteractionId) {
            pendingAnnouncements.delete(originalInteractionId);
        }
        await interaction.update({
            content: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
            embeds: [],
            components: []
        });
    }
    /**
     * Handle help command buttons
     */
    async handleHelpButton(interaction, customId) {
        const commandName = customId.replace('help_', '');
        // Commands that can be executed directly
        const instantTriggerCommands = ['balance', 'roles', 'leaderboard', 'history'];
        if (instantTriggerCommands.includes(commandName)) {
            // Execute the command directly
            const command = this.app.client.commands.get(commandName);
            if (command) {
                await command.execute(interaction);
            }
            else {
                await interaction.reply({
                    content: `Command "${commandName}" not found.`,
                    ephemeral: true
                });
            }
        }
        else {
            // For other commands, show usage information
            const commandDescriptions = {
                'pay': 'Transfer coins to another user. Usage: `/pay @user amount`',
                'addrole': '**[Admin Only]** Add a role achievement. Usage: `/addrole @role price`',
                'editrole': '**[Admin Only]** Edit a role achievement. Usage: `/editrole role_name new_price`',
                'removerole': '**[Admin Only]** Remove a role achievement. Usage: `/removerole role_name`',
                'give': '**[Admin Only]** Give coins to a user. Usage: `/give @user amount`',
                'fine': '**[Admin Only]** Remove coins from a user. Usage: `/fine @user amount`',
                'tax': '**[Admin Only]** Configure automatic taxation system. Usage: `/tax status:on/off frequency:weeks amount:plc role:@role`',
                'starterbalance': '**[Admin Only]** Manage starter balance rules. Usage: `/starterbalance action:add/edit/remove/list role:@role amount:plc`',
                'incomecredentials': '**[Admin Only]** Customize income earning guide text displayed in /help command. Supports line breaks (\\n) and formatting. Usage: `/incomecredentials text:"Your custom income guide text"`',
                'automessage': '**[Admin Only]** Manage automated messages for server events. Usage: `/automessage action:create trigger:member_join delivery:channel name:welcome title:Welcome! description:Hello {user}!`',
                'placeholders': 'View available placeholders for automated messages. Usage: `/placeholders`',
                'testcleanup': '**[Admin Only]** Test user data cleanup functionality. Usage: `/testcleanup user:@user action:check/simulate`'
            };
            const description = commandDescriptions[commandName] || `Use the /${commandName} command.`;
            await interaction.reply({
                content: `**/${commandName}**\n${description}`,
                ephemeral: true
            });
        }
    }
    /**
     * Handle trade button interactions
     */
    async handleTradeButton(interaction, customId) {
        try {
            // Get TradeService
            const tradeService = this.app.getService('TradeService');
            // Parse button action and trade ID
            const parts = customId.split('_');
            if (parts.length < 3) {
                throw new errorHandler_1.ValidationError('Invalid trade button format');
            }
            const action = parts[1]; // accept, decline, confirm, dispute, cancel, details
            const tradeId = parts.slice(2).join('_'); // Handle trade IDs with underscores
            // Get the trade
            const trade = await tradeService.getTrade(tradeId);
            if (!trade) {
                throw new errorHandler_1.ValidationError('Trade not found');
            }
            // Verify user is involved in the trade
            if (!trade.involvesUser(interaction.user.id)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            switch (action) {
                case 'accept':
                    await this.handleTradeAccept(interaction, trade, tradeService);
                    break;
                case 'decline':
                    await this.handleTradeDecline(interaction, trade, tradeService);
                    break;
                case 'confirm':
                    await this.handleTradeConfirm(interaction, trade, tradeService);
                    break;
                case 'dispute':
                    await this.handleTradeDispute(interaction, trade, tradeService);
                    break;
                case 'cancel':
                    await this.handleTradeCancel(interaction, trade, tradeService);
                    break;
                case 'details':
                    await this.handleTradeDetails(interaction, trade);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown trade action: ${action}`);
            }
        }
        catch (error) {
            this.logger.error('Error handling trade button', { error, customId, userId: interaction.user.id });
            const embed = (0, embedBuilder_1.createErrorEmbed)('Trade Action Failed', error instanceof errorHandler_1.ValidationError ? error.message : 'An unexpected error occurred');
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            }
            else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        }
    }
    async handleTradeAccept(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        const updatedTrade = await tradeService.acceptTrade(trade.tradeId, interaction.user.id, interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Trade Accepted!**\n\nThe trade has been activated and funds have been locked in escrow. You will receive further instructions via DM.`
        });
    }
    async handleTradeDecline(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        await tradeService.cancelTrade(trade.tradeId, interaction.user.id, 'Trade declined', interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Declined**\n\nThe trade proposal has been declined and cancelled.`
        });
    }
    async handleTradeConfirm(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        const result = await tradeService.confirmTrade(trade.tradeId, interaction.user.id, interaction.client);
        if (result.completed) {
            await interaction.editReply({
                content: `${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Trade Completed!**\n\nBoth parties have confirmed completion. Funds have been released to the seller.`
            });
        }
        else {
            await interaction.editReply({
                content: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Confirmation Recorded**\n\nYour confirmation has been recorded. Waiting for the other party to confirm.`
            });
        }
    }
    async handleTradeDispute(interaction, trade, tradeService) {
        await interaction.reply({
            content: `${embedBuilder_1.EMOJIS.TRADE.DISPUTED} **Dispute System**\n\nThe dispute system will be implemented in the next phase. For now, please contact an administrator for assistance.`,
            ephemeral: true
        });
    }
    async handleTradeCancel(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        await tradeService.cancelTrade(trade.tradeId, interaction.user.id, 'Cancelled by user', interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Cancelled**\n\nThe trade has been cancelled and any escrowed funds have been refunded.`
        });
    }
    async handleTradeDetails(interaction, trade) {
        // Implementation for detailed trade view will be added with more UI components
        await interaction.reply({
            content: `${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} **Trade Details**\n\nDetailed trade view will be implemented in the next phase.`,
            ephemeral: true
        });
    }
}
exports.InteractionCreateEventHandler = InteractionCreateEventHandler;
//# sourceMappingURL=interactionCreate.js.map