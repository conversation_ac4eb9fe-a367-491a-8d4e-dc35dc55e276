import { Document } from 'mongoose';
export interface IEscrowTransaction extends Document {
    escrowId: string;
    tradeId: string;
    discordId: string;
    guildId: string;
    amount: number;
    transactionType: 'LOCK' | 'RELEASE' | 'REFUND' | 'DISPUTE_HOLD';
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REVERSED';
    timestamp: Date;
    completedAt?: Date;
    relatedTransactionId?: string;
    reversalTransactionId?: string;
    details?: string;
    adminId?: string;
    reason?: string;
}
declare const _default: import("mongoose").Model<IEscrowTransaction, {}, {}, {}, Document<unknown, {}, IEscrowTransaction, {}> & IEscrowTransaction & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=EscrowTransaction.d.ts.map