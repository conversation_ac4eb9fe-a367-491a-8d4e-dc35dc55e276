"use strict";
/**
 * Trade Background Service
 * Handles automated background tasks for the trade system
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeBackgroundService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const BaseService_1 = require("../base/BaseService");
const decorators_1 = require("../../core/decorators");
const constants_1 = require("../../config/constants");
const models_1 = require("../../models");
const EscrowManager_1 = require("./managers/EscrowManager");
const TradeNotificationManager_1 = require("./managers/TradeNotificationManager");
/**
 * Trade Background Service Class
 */
class TradeBackgroundService extends BaseService_1.BaseService {
    constructor(app) {
        super('TradeBackgroundService', app);
        this.client = null;
        // Task intervals
        this.expirationCheckInterval = null;
        this.warningCheckInterval = null;
        this.cleanupInterval = null;
        this.healthCheckInterval = null;
        // Task statistics
        this.taskStats = {
            expiredTrades: 0,
            warningsSent: 0,
            cleanupOperations: 0,
            errors: 0,
            lastRun: new Date()
        };
        this.escrowManager = new EscrowManager_1.EscrowManager(app);
        this.notificationManager = new TradeNotificationManager_1.TradeNotificationManager(app);
    }
    /**
     * Initialize the background service
     */
    async onInitialize() {
        this.logger.info('[TradeBackgroundService] Trade background service initialized');
        // Initialize sub-managers
        await this.escrowManager.initialize();
        await this.notificationManager.initialize();
        // Start background tasks
        this.startBackgroundTasks();
    }
    /**
     * Set Discord client for notifications
     */
    setClient(client) {
        this.client = client;
    }
    /**
     * Start all background tasks
     */
    startBackgroundTasks() {
        this.logger.info('[TradeBackgroundService] Starting background tasks');
        // Check for expired trades every 5 minutes
        this.expirationCheckInterval = setInterval(async () => {
            try {
                await this.processExpiredTrades();
            }
            catch (error) {
                this.handleError(error, { operation: 'process_expired_trades' });
                this.taskStats.errors++;
            }
        }, 5 * 60 * 1000);
        // Check for trades needing warnings every 30 minutes
        this.warningCheckInterval = setInterval(async () => {
            try {
                await this.sendTradeWarnings();
            }
            catch (error) {
                this.handleError(error, { operation: 'send_trade_warnings' });
                this.taskStats.errors++;
            }
        }, 30 * 60 * 1000);
        // Cleanup operations every hour
        this.cleanupInterval = setInterval(async () => {
            try {
                await this.performCleanupOperations();
            }
            catch (error) {
                this.handleError(error, { operation: 'cleanup_operations' });
                this.taskStats.errors++;
            }
        }, 60 * 60 * 1000);
        // Health check every 15 minutes
        this.healthCheckInterval = setInterval(async () => {
            try {
                await this.performHealthCheck();
            }
            catch (error) {
                this.handleError(error, { operation: 'health_check' });
                this.taskStats.errors++;
            }
        }, 15 * 60 * 1000);
        this.logger.info('[TradeBackgroundService] Background tasks started');
    }
    /**
     * Stop all background tasks
     */
    async onShutdown() {
        this.logger.info('[TradeBackgroundService] Stopping background tasks');
        if (this.expirationCheckInterval) {
            clearInterval(this.expirationCheckInterval);
            this.expirationCheckInterval = null;
        }
        if (this.warningCheckInterval) {
            clearInterval(this.warningCheckInterval);
            this.warningCheckInterval = null;
        }
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
        if (this.healthCheckInterval) {
            clearInterval(this.healthCheckInterval);
            this.healthCheckInterval = null;
        }
        this.logger.info('[TradeBackgroundService] Background tasks stopped');
    }
    /**
     * Process expired trades
     */
    async processExpiredTrades() {
        this.logOperation('Processing expired trades');
        try {
            const now = new Date();
            // Find expired trades that are still active
            const expiredTrades = await models_1.Trade.find({
                expiresAt: { $lt: now },
                state: { $in: [constants_1.TRADE.STATES.PROPOSED, constants_1.TRADE.STATES.ACCEPTED, constants_1.TRADE.STATES.ACTIVE] }
            });
            let processedCount = 0;
            for (const trade of expiredTrades) {
                const session = await mongoose_1.default.startSession();
                try {
                    await session.withTransaction(async () => {
                        // Refund any escrowed funds
                        if (trade.escrowLocked && trade.escrowAmount > 0) {
                            await this.escrowManager.refundEscrow(trade, 'Trade expired', session);
                        }
                        // Update trade state
                        trade.state = constants_1.TRADE.STATES.EXPIRED;
                        await trade.save({ session });
                        // Update user stats
                        await this.updateUserTradeStats(trade.sellerId, trade.guildId, 'TRADE_EXPIRED', session);
                        await this.updateUserTradeStats(trade.buyerId, trade.guildId, 'TRADE_EXPIRED', session);
                        processedCount++;
                    });
                    // Send notifications (outside transaction)
                    if (this.client) {
                        try {
                            await this.notificationManager.sendTradeExpired(trade, this.client);
                        }
                        catch (notificationError) {
                            this.logger.warn('Failed to send expiration notification', {
                                tradeId: trade.tradeId,
                                error: notificationError
                            });
                        }
                    }
                    this.logOperation('Trade expired and processed', { tradeId: trade.tradeId });
                }
                catch (error) {
                    this.handleError(error, { operation: 'process_single_expired_trade', tradeId: trade.tradeId });
                }
                finally {
                    await session.endSession();
                }
            }
            this.taskStats.expiredTrades += processedCount;
            this.taskStats.lastRun = new Date();
            if (processedCount > 0) {
                this.logger.info(`[TradeBackgroundService] Processed ${processedCount} expired trades`);
            }
            return processedCount;
        }
        catch (error) {
            this.handleError(error, { operation: 'process_expired_trades' });
            throw error;
        }
    }
    /**
     * Send warning notifications for trades nearing expiration
     */
    async sendTradeWarnings() {
        this.logOperation('Sending trade warnings');
        if (!this.client) {
            this.logger.warn('Cannot send trade warnings: Discord client not available');
            return 0;
        }
        try {
            let warningsSent = 0;
            // Check for each warning threshold
            for (const warningHours of constants_1.TRADE.WARNING_HOURS) {
                const warningTime = new Date();
                warningTime.setHours(warningTime.getHours() + warningHours);
                // Find trades that will expire within the warning window
                const tradesNeedingWarning = await models_1.Trade.find({
                    expiresAt: {
                        $gte: new Date(), // Not yet expired
                        $lte: warningTime // But will expire within warning window
                    },
                    state: { $in: [constants_1.TRADE.STATES.PROPOSED, constants_1.TRADE.STATES.ACCEPTED, constants_1.TRADE.STATES.ACTIVE] },
                    $or: [
                        { lastWarningAt: { $exists: false } },
                        { lastWarningAt: { $lt: new Date(Date.now() - 30 * 60 * 1000) } } // Last warning was more than 30 minutes ago
                    ]
                });
                for (const trade of tradesNeedingWarning) {
                    try {
                        await this.notificationManager.sendTradeWarning(trade, warningHours, this.client);
                        // Update last warning time
                        trade.lastWarningAt = new Date();
                        trade.warningsSent++;
                        await trade.save();
                        warningsSent++;
                        this.logOperation('Trade warning sent', {
                            tradeId: trade.tradeId,
                            hoursRemaining: warningHours
                        });
                    }
                    catch (error) {
                        this.logger.warn('Failed to send trade warning', {
                            tradeId: trade.tradeId,
                            error
                        });
                    }
                }
            }
            this.taskStats.warningsSent += warningsSent;
            if (warningsSent > 0) {
                this.logger.info(`[TradeBackgroundService] Sent ${warningsSent} trade warnings`);
            }
            return warningsSent;
        }
        catch (error) {
            this.handleError(error, { operation: 'send_trade_warnings' });
            throw error;
        }
    }
    /**
     * Perform cleanup operations
     */
    async performCleanupOperations() {
        this.logOperation('Performing cleanup operations');
        try {
            let cleanupCount = 0;
            // Clean up old completed/cancelled trades (older than 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            // Archive old trades (you might want to move them to an archive collection instead of deleting)
            const oldTrades = await models_1.Trade.find({
                state: { $in: [constants_1.TRADE.STATES.COMPLETED, constants_1.TRADE.STATES.CANCELLED, constants_1.TRADE.STATES.EXPIRED] },
                $or: [
                    { completedAt: { $lt: thirtyDaysAgo } },
                    { createdAt: { $lt: thirtyDaysAgo } }
                ]
            });
            // For now, we'll just log them. In production, you might want to:
            // 1. Move to archive collection
            // 2. Export to external storage
            // 3. Delete after longer period
            if (oldTrades.length > 0) {
                this.logger.info(`[TradeBackgroundService] Found ${oldTrades.length} old trades for potential archival`);
            }
            // Clean up orphaned escrow transactions
            const orphanedEscrow = await models_1.EscrowTransaction.find({
                status: 'PENDING',
                timestamp: { $lt: thirtyDaysAgo }
            });
            for (const escrow of orphanedEscrow) {
                try {
                    escrow.status = 'FAILED';
                    await escrow.save();
                    cleanupCount++;
                    this.logger.warn('Cleaned up orphaned escrow transaction', {
                        escrowId: escrow.escrowId,
                        tradeId: escrow.tradeId
                    });
                }
                catch (error) {
                    this.logger.error('Failed to cleanup orphaned escrow', {
                        escrowId: escrow.escrowId,
                        error
                    });
                }
            }
            // Reset daily trade counts for users (this should happen automatically, but as a backup)
            const usersToReset = await models_1.UserTradeStats.find({
                lastResetDate: { $lt: new Date(Date.now() - 25 * 60 * 60 * 1000) }, // More than 25 hours ago
                dailyTradeCount: { $gt: 0 }
            });
            for (const userStats of usersToReset) {
                userStats.resetDailyCountIfNeeded();
                await userStats.save();
                cleanupCount++;
            }
            this.taskStats.cleanupOperations += cleanupCount;
            if (cleanupCount > 0) {
                this.logger.info(`[TradeBackgroundService] Performed ${cleanupCount} cleanup operations`);
            }
            return cleanupCount;
        }
        catch (error) {
            this.handleError(error, { operation: 'cleanup_operations' });
            throw error;
        }
    }
    /**
     * Perform system health check
     */
    async performHealthCheck() {
        this.logOperation('Performing health check');
        try {
            // Check for stuck trades
            const stuckTrades = await models_1.Trade.find({
                state: constants_1.TRADE.STATES.ACTIVE,
                escrowLocked: true,
                expiresAt: { $gt: new Date() }, // Not expired
                $or: [
                    { sellerConfirmed: true, buyerConfirmed: false, sellerConfirmedAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } },
                    { sellerConfirmed: false, buyerConfirmed: true, buyerConfirmedAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } }
                ]
            });
            if (stuckTrades.length > 0) {
                this.logger.warn(`[TradeBackgroundService] Found ${stuckTrades.length} potentially stuck trades`);
                // You might want to send alerts to admins here
                for (const trade of stuckTrades) {
                    this.logger.warn('Stuck trade detected', {
                        tradeId: trade.tradeId,
                        sellerConfirmed: trade.sellerConfirmed,
                        buyerConfirmed: trade.buyerConfirmed,
                        hoursStuck: (Date.now() - Math.max(trade.sellerConfirmedAt?.getTime() || 0, trade.buyerConfirmedAt?.getTime() || 0)) / (1000 * 60 * 60)
                    });
                }
            }
            // Check escrow consistency
            const inconsistentTrades = await models_1.Trade.find({
                escrowLocked: true,
                escrowAmount: { $gt: 0 },
                state: { $in: [constants_1.TRADE.STATES.COMPLETED, constants_1.TRADE.STATES.CANCELLED, constants_1.TRADE.STATES.EXPIRED] }
            });
            if (inconsistentTrades.length > 0) {
                this.logger.error(`[TradeBackgroundService] Found ${inconsistentTrades.length} trades with inconsistent escrow state`);
                for (const trade of inconsistentTrades) {
                    this.logger.error('Escrow inconsistency detected', {
                        tradeId: trade.tradeId,
                        state: trade.state,
                        escrowLocked: trade.escrowLocked,
                        escrowAmount: trade.escrowAmount
                    });
                }
            }
            // Log health check completion
            this.logger.debug('[TradeBackgroundService] Health check completed', {
                stuckTrades: stuckTrades.length,
                inconsistentTrades: inconsistentTrades.length
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'health_check' });
            throw error;
        }
    }
    /**
     * Get background task statistics
     */
    getTaskStats() {
        return { ...this.taskStats };
    }
    /**
     * Reset task statistics
     */
    resetTaskStats() {
        this.taskStats = {
            expiredTrades: 0,
            warningsSent: 0,
            cleanupOperations: 0,
            errors: 0,
            lastRun: new Date()
        };
    }
    // Private helper methods
    async updateUserTradeStats(discordId, guildId, action, session) {
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId }).session(session);
            if (userStats) {
                switch (action) {
                    case 'TRADE_EXPIRED':
                        userStats.expiredTrades++;
                        userStats.activeTrades = Math.max(0, userStats.activeTrades - 1);
                        break;
                }
                await userStats.save({ session });
            }
        }
        catch (error) {
            this.logger.warn('Failed to update user trade stats', { error, discordId, action });
        }
    }
}
exports.TradeBackgroundService = TradeBackgroundService;
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeBackgroundService.prototype, "onInitialize", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeBackgroundService.prototype, "processExpiredTrades", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeBackgroundService.prototype, "sendTradeWarnings", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeBackgroundService.prototype, "performCleanupOperations", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeBackgroundService.prototype, "performHealthCheck", null);
//# sourceMappingURL=TradeBackgroundService.js.map