"use strict";
/**
 * Trade Service
 * Core service for managing secure trades between users
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeService = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const discord_js_1 = require("discord.js");
const BaseService_1 = require("../base/BaseService");
const decorators_1 = require("../../core/decorators");
const errorHandler_1 = require("../../utils/errorHandler");
const constants_1 = require("../../config/constants");
const models_1 = require("../../models");
const EscrowManager_1 = require("./managers/EscrowManager");
const TradeValidator_1 = require("./managers/TradeValidator");
const TradeNotificationManager_1 = require("./managers/TradeNotificationManager");
const TradeSecurityService_1 = require("./TradeSecurityService");
/**
 * Main Trade Service Class
 */
class TradeService extends BaseService_1.BaseService {
    constructor(app) {
        super('TradeService', app);
        this.escrowManager = new EscrowManager_1.EscrowManager(app);
        this.validator = new TradeValidator_1.TradeValidator(app);
        this.notificationManager = new TradeNotificationManager_1.TradeNotificationManager(app);
        this.securityService = new TradeSecurityService_1.TradeSecurityService(app);
    }
    /**
     * Initialize the trade service
     */
    async onInitialize() {
        this.logger.info('[TradeService] Trade system initialized');
        // Initialize sub-managers
        await this.escrowManager.initialize();
        await this.validator.initialize();
        await this.notificationManager.initialize();
        await this.securityService.initialize();
    }
    /**
     * Create a new trade proposal
     */
    async createTrade(params, client) {
        this.logOperation('Creating new trade', params);
        // Check rate limits and security
        const rateLimitCheck = await this.securityService.checkTradeRateLimit(params.sellerId, params.guildId);
        if (!rateLimitCheck.allowed) {
            throw new errorHandler_1.ValidationError(rateLimitCheck.reason || 'Rate limit exceeded');
        }
        const buyerRateLimitCheck = await this.securityService.checkTradeRateLimit(params.buyerId, params.guildId);
        if (!buyerRateLimitCheck.allowed) {
            throw new errorHandler_1.ValidationError(`Buyer ${buyerRateLimitCheck.reason || 'rate limit exceeded'}`);
        }
        // Validate trade creation
        await this.validator.validateTradeCreation(params);
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                // Generate unique trade ID
                const tradeId = this.generateTradeId();
                // Calculate expiration time
                const expiresAt = new Date();
                expiresAt.setHours(expiresAt.getHours() + constants_1.TRADE.TRADE_EXPIRATION_HOURS);
                // Create trade record
                const trade = await models_1.Trade.create([{
                        tradeId,
                        sellerId: params.sellerId,
                        buyerId: params.buyerId,
                        guildId: params.guildId,
                        amount: params.amount,
                        itemDescription: params.itemDescription,
                        notes: params.notes,
                        state: constants_1.TRADE.STATES.PROPOSED,
                        initiatedBy: params.initiatedBy,
                        expiresAt,
                        escrowLocked: false,
                        escrowAmount: 0,
                        sellerConfirmed: false,
                        buyerConfirmed: false,
                        warningsSent: 0,
                        extensionGranted: false
                    }], { session });
                // Update user trade stats
                await this.updateUserTradeStats(params.sellerId, params.guildId, 'TRADE_INITIATED', session);
                await this.updateUserTradeStats(params.buyerId, params.guildId, 'TRADE_INITIATED', session);
                // Perform security check on the created trade
                const securityCheck = await this.securityService.performSecurityCheck(trade[0]);
                if (!securityCheck.passed) {
                    this.logger.warn('Trade failed security check', {
                        tradeId,
                        violations: securityCheck.violations,
                        riskLevel: securityCheck.riskLevel
                    });
                    // For now, we'll log but not block. In production, you might want to:
                    // - Block CRITICAL risk trades
                    // - Flag HIGH risk trades for manual review
                    // - Apply additional restrictions
                }
                this.logOperation('Trade created successfully', { tradeId, state: 'PROPOSED' });
                // Send notifications (outside transaction)
                if (client) {
                    setImmediate(async () => {
                        try {
                            await this.notificationManager.sendTradeProposal(trade[0], client);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'trade_proposal_notification' });
                        }
                    });
                }
                return trade[0];
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'create_trade', params });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    /**
     * Accept a trade proposal
     */
    async acceptTrade(tradeId, acceptingUserId, client) {
        this.logOperation('Accepting trade', { tradeId, acceptingUserId });
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                // Get and validate trade
                const trade = await models_1.Trade.findOne({ tradeId }).session(session);
                if (!trade) {
                    throw new errorHandler_1.ValidationError('Trade not found');
                }
                // Validate acceptance
                await this.validator.validateTradeAcceptance(trade, acceptingUserId);
                // Check buyer has sufficient balance
                const buyer = await models_1.User.findOne({ discordId: trade.buyerId }).session(session);
                if (!buyer || buyer.balance < trade.amount) {
                    throw new errorHandler_1.ValidationError('Insufficient balance to accept trade');
                }
                // Lock escrow funds
                await this.escrowManager.lockEscrow(trade, session);
                // Update trade state
                trade.state = constants_1.TRADE.STATES.ACCEPTED;
                trade.acceptedAt = new Date();
                await trade.save({ session });
                // Create acceptance confirmation
                await models_1.TradeConfirmation.create([{
                        confirmationId: this.generateConfirmationId(),
                        tradeId: trade.tradeId,
                        discordId: acceptingUserId,
                        guildId: trade.guildId,
                        confirmationType: 'TRADE_ACCEPTANCE',
                        confirmed: true,
                        confirmedAt: new Date()
                    }], { session });
                // Transition to ACTIVE state
                await this.transitionToActive(trade, session);
                this.logOperation('Trade accepted and activated', { tradeId, state: 'ACTIVE' });
                // Send notifications (outside transaction)
                if (client) {
                    setImmediate(async () => {
                        try {
                            await this.notificationManager.sendTradeAccepted(trade, client);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'trade_accepted_notification' });
                        }
                    });
                }
                return trade;
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'accept_trade', tradeId, acceptingUserId });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    /**
     * Confirm trade completion by a party
     */
    async confirmTrade(tradeId, confirmingUserId, client) {
        this.logOperation('Confirming trade', { tradeId, confirmingUserId });
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                // Get and validate trade
                const trade = await models_1.Trade.findOne({ tradeId }).session(session);
                if (!trade) {
                    throw new errorHandler_1.ValidationError('Trade not found');
                }
                // Validate confirmation
                await this.validator.validateTradeConfirmation(trade, confirmingUserId);
                // Update confirmation status
                if (trade.sellerId === confirmingUserId) {
                    trade.sellerConfirmed = true;
                    trade.sellerConfirmedAt = new Date();
                }
                else {
                    trade.buyerConfirmed = true;
                    trade.buyerConfirmedAt = new Date();
                }
                // Create confirmation record
                await models_1.TradeConfirmation.create([{
                        confirmationId: this.generateConfirmationId(),
                        tradeId: trade.tradeId,
                        discordId: confirmingUserId,
                        guildId: trade.guildId,
                        confirmationType: 'TRADE_COMPLETION',
                        confirmed: true,
                        confirmedAt: new Date()
                    }], { session });
                let completed = false;
                // Check if both parties confirmed
                if (trade.sellerConfirmed && trade.buyerConfirmed) {
                    // Complete the trade
                    await this.completeTrade(trade, session);
                    completed = true;
                }
                else {
                    // Save partial confirmation
                    await trade.save({ session });
                    // Extend expiration if only one party confirmed
                    if (!trade.extensionGranted) {
                        const newExpiration = new Date();
                        newExpiration.setHours(newExpiration.getHours() + constants_1.TRADE.PARTIAL_CONFIRMATION_EXTENSION_HOURS);
                        trade.expiresAt = newExpiration;
                        trade.extensionGranted = true;
                        await trade.save({ session });
                    }
                }
                this.logOperation('Trade confirmation processed', {
                    tradeId,
                    confirmingUserId,
                    completed,
                    bothConfirmed: trade.sellerConfirmed && trade.buyerConfirmed
                });
                // Send notifications (outside transaction)
                if (client) {
                    setImmediate(async () => {
                        try {
                            if (completed) {
                                await this.notificationManager.sendTradeCompleted(trade, client);
                            }
                            else {
                                await this.notificationManager.sendPartialConfirmation(trade, confirmingUserId, client);
                            }
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'trade_confirmation_notification' });
                        }
                    });
                }
                return { trade, completed };
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'confirm_trade', tradeId, confirmingUserId });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    /**
     * Cancel a trade
     */
    async cancelTrade(tradeId, cancellingUserId, reason, client) {
        this.logOperation('Cancelling trade', { tradeId, cancellingUserId, reason });
        const session = await mongoose_1.default.startSession();
        try {
            return await session.withTransaction(async () => {
                // Get and validate trade
                const trade = await models_1.Trade.findOne({ tradeId }).session(session);
                if (!trade) {
                    throw new errorHandler_1.ValidationError('Trade not found');
                }
                // Validate cancellation
                await this.validator.validateTradeCancellation(trade, cancellingUserId);
                // Release any escrowed funds
                if (trade.escrowLocked) {
                    await this.escrowManager.refundEscrow(trade, 'Trade cancelled', session);
                }
                // Update trade state
                trade.state = constants_1.TRADE.STATES.CANCELLED;
                await trade.save({ session });
                // Update user stats
                await this.updateUserTradeStats(trade.sellerId, trade.guildId, 'TRADE_CANCELLED', session);
                await this.updateUserTradeStats(trade.buyerId, trade.guildId, 'TRADE_CANCELLED', session);
                this.logOperation('Trade cancelled', { tradeId, reason });
                // Send notifications (outside transaction)
                if (client) {
                    setImmediate(async () => {
                        try {
                            await this.notificationManager.sendTradeCancelled(trade, cancellingUserId, reason, client);
                        }
                        catch (error) {
                            this.handleError(error, { operation: 'trade_cancelled_notification' });
                        }
                    });
                }
                return trade;
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'cancel_trade', tradeId, cancellingUserId });
            throw error;
        }
        finally {
            await session.endSession();
        }
    }
    /**
     * Get trade by ID
     */
    async getTrade(tradeId) {
        try {
            return await models_1.Trade.findOne({ tradeId }).lean();
        }
        catch (error) {
            this.handleError(error, { operation: 'get_trade', tradeId });
            throw error;
        }
    }
    /**
     * Get user's active trades
     */
    async getUserActiveTrades(discordId, guildId) {
        try {
            const query = {
                $or: [
                    { sellerId: discordId },
                    { buyerId: discordId }
                ],
                state: { $in: [constants_1.TRADE.STATES.PROPOSED, constants_1.TRADE.STATES.ACCEPTED, constants_1.TRADE.STATES.ACTIVE] }
            };
            if (guildId) {
                query.guildId = guildId;
            }
            return await models_1.Trade.find(query)
                .sort({ createdAt: -1 })
                .lean();
        }
        catch (error) {
            this.handleError(error, { operation: 'get_user_active_trades', discordId });
            throw error;
        }
    }
    // Private helper methods
    async transitionToActive(trade, session) {
        trade.state = constants_1.TRADE.STATES.ACTIVE;
        await trade.save({ session });
        // Update user stats for active trade
        await this.updateUserTradeStats(trade.sellerId, trade.guildId, 'TRADE_ACTIVATED', session);
        await this.updateUserTradeStats(trade.buyerId, trade.guildId, 'TRADE_ACTIVATED', session);
    }
    async completeTrade(trade, session) {
        // Release escrow to seller
        await this.escrowManager.releaseEscrow(trade, 'Trade completed successfully', session);
        // Update trade state
        trade.state = constants_1.TRADE.STATES.COMPLETED;
        trade.completedAt = new Date();
        await trade.save({ session });
        // Calculate completion time
        const completionTimeHours = trade.acceptedAt ?
            (trade.completedAt.getTime() - trade.acceptedAt.getTime()) / (1000 * 60 * 60) : 0;
        // Update user stats
        await this.updateUserTradeStats(trade.sellerId, trade.guildId, 'TRADE_COMPLETED', session, { tradeAmount: trade.amount, completionTimeHours, asSeller: true });
        await this.updateUserTradeStats(trade.buyerId, trade.guildId, 'TRADE_COMPLETED', session, { tradeAmount: trade.amount, completionTimeHours, asSeller: false });
        // Check for automatic restrictions (outside transaction to avoid blocking)
        setImmediate(async () => {
            try {
                await this.securityService.applyAutomaticRestrictions(trade.sellerId, trade.guildId);
                await this.securityService.applyAutomaticRestrictions(trade.buyerId, trade.guildId);
            }
            catch (error) {
                this.logger.warn('Error applying automatic restrictions', { error, tradeId: trade.tradeId });
            }
        });
    }
    async updateUserTradeStats(discordId, guildId, action, session, tradeData) {
        try {
            // Get or create user trade stats
            let userStats = await models_1.UserTradeStats.findOne({ discordId, guildId }).session(session);
            if (!userStats) {
                userStats = new models_1.UserTradeStats({
                    discordId,
                    guildId,
                    totalTrades: 0,
                    successfulTrades: 0,
                    cancelledTrades: 0,
                    expiredTrades: 0,
                    disputedTrades: 0,
                    tradesAsSeller: 0,
                    tradesAsBuyer: 0,
                    totalVolumeTraded: 0,
                    averageTradeValue: 0,
                    largestTrade: 0,
                    reputationScore: 50,
                    disputeRatio: 0,
                    completionRate: 0,
                    averageCompletionTime: 0,
                    fastestCompletion: 0,
                    activeTrades: 0,
                    isRestricted: false,
                    dailyTradeCount: 0,
                    lastTradeDate: new Date(),
                    lastResetDate: new Date(),
                    lastUpdated: new Date(),
                    warningsReceived: 0,
                    violationHistory: []
                });
            }
            // Update stats based on action
            switch (action) {
                case 'TRADE_INITIATED':
                    userStats.resetDailyCountIfNeeded();
                    userStats.dailyTradeCount++;
                    userStats.lastTradeDate = new Date();
                    break;
                case 'TRADE_ACTIVATED':
                    userStats.activeTrades++;
                    break;
                case 'TRADE_COMPLETED':
                    if (tradeData) {
                        userStats.updateAfterTrade(tradeData.tradeAmount, tradeData.completionTimeHours, true, // wasSuccessful
                        false, // wasDisputed
                        tradeData.asSeller);
                    }
                    break;
                case 'TRADE_CANCELLED':
                    userStats.cancelledTrades++;
                    userStats.activeTrades = Math.max(0, userStats.activeTrades - 1);
                    break;
                case 'TRADE_EXPIRED':
                    userStats.expiredTrades++;
                    userStats.activeTrades = Math.max(0, userStats.activeTrades - 1);
                    break;
                case 'TRADE_DISPUTED':
                    userStats.disputedTrades++;
                    break;
            }
            await userStats.save({ session });
            this.logOperation('User trade stats updated', {
                discordId,
                action,
                activeTrades: userStats.activeTrades,
                totalTrades: userStats.totalTrades
            });
        }
        catch (error) {
            this.handleError(error, { operation: 'update_user_trade_stats', discordId, action });
            // Don't throw here as this is a secondary operation
        }
    }
    generateTradeId() {
        return `trade_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateConfirmationId() {
        return `conf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.TradeService = TradeService;
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "onInitialize", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "createTrade", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "acceptTrade", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "confirmTrade", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, discord_js_1.Client]),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "cancelTrade", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "getTrade", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], TradeService.prototype, "getUserActiveTrades", null);
//# sourceMappingURL=TradeService.js.map