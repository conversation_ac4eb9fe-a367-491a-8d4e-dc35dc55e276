/**
 * Trade Admin Command
 * Administrative commands for managing the trade system
 */
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { TradeService } from '../../services/trade/TradeService';
import { DisputeService } from '../../services/trade/DisputeService';
/**
 * Trade Admin command implementation
 */
export declare class TradeAdminCommand extends BaseCommand {
    private tradeService;
    private disputeService;
    constructor();
    /**
     * Set the trade services (dependency injection)
     */
    setTradeServices(tradeService: TradeService, disputeService: DisputeService): void;
    /**
     * Customize the command builder with subcommands
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the trade admin command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
    /**
     * <PERSON>le disputes subcommand
     */
    private handleDisputes;
    /**
     * Handle resolve subcommand
     */
    private handleResolve;
    /**
     * <PERSON>le cancel subcommand
     */
    private handleCancel;
    /**
     * Handle stats subcommand
     */
    private handleStats;
    /**
     * Handle restrict subcommand
     */
    private handleRestrict;
    /**
     * Handle unrestrict subcommand
     */
    private handleUnrestrict;
    /**
     * Handle overview subcommand
     */
    private handleOverview;
}
//# sourceMappingURL=TradeAdminCommand.d.ts.map