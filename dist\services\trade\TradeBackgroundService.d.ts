/**
 * Trade Background Service
 * Handles automated background tasks for the trade system
 */
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
export interface BackgroundTaskStats {
    expiredTrades: number;
    warningsSent: number;
    cleanupOperations: number;
    errors: number;
    lastRun: Date;
}
/**
 * Trade Background Service Class
 */
export declare class TradeBackgroundService extends BaseService {
    private escrowManager;
    private notificationManager;
    private client;
    private expirationCheckInterval;
    private warningCheckInterval;
    private cleanupInterval;
    private healthCheckInterval;
    private taskStats;
    constructor(app: any);
    /**
     * Initialize the background service
     */
    onInitialize(): Promise<void>;
    /**
     * Set Discord client for notifications
     */
    setClient(client: Client): void;
    /**
     * Start all background tasks
     */
    private startBackgroundTasks;
    /**
     * Stop all background tasks
     */
    onShutdown(): Promise<void>;
    /**
     * Process expired trades
     */
    processExpiredTrades(): Promise<number>;
    /**
     * Send warning notifications for trades nearing expiration
     */
    sendTradeWarnings(): Promise<number>;
    /**
     * Perform cleanup operations
     */
    performCleanupOperations(): Promise<number>;
    /**
     * Perform system health check
     */
    performHealthCheck(): Promise<void>;
    /**
     * Get background task statistics
     */
    getTaskStats(): BackgroundTaskStats;
    /**
     * Reset task statistics
     */
    resetTaskStats(): void;
    private updateUserTradeStats;
}
//# sourceMappingURL=TradeBackgroundService.d.ts.map