/**
 * Trade Validator
 * Handles validation logic for trade operations
 */
import { BaseService } from '../../base/BaseService';
import { ITrade } from '../../../models';
import { TradeCreationParams } from '../TradeService';
/**
 * Trade Validator Class
 */
export declare class TradeValidator extends BaseService {
    constructor(app: any);
    /**
     * Initialize the trade validator
     */
    initialize(): Promise<void>;
    /**
     * Validate trade creation parameters
     */
    validateTradeCreation(params: TradeCreationParams): Promise<void>;
    /**
     * Validate trade acceptance
     */
    validateTradeAcceptance(trade: ITrade, acceptingUserId: string): Promise<void>;
    /**
     * Validate trade confirmation
     */
    validateTradeConfirmation(trade: ITrade, confirmingUserId: string): Promise<void>;
    /**
     * Validate trade cancellation
     */
    validateTradeCancellation(trade: ITrade, cancellingUserId: string): Promise<void>;
    /**
     * Validate dispute initiation
     */
    validateDisputeInitiation(trade: ITrade, disputingUserId: string): Promise<void>;
    private validateBasicTradeParams;
    private validateTradeParties;
    private validateNoActiveTradeBetweenParties;
    private validateUserTradeEligibility;
    private validateBuyerBalance;
}
//# sourceMappingURL=TradeValidator.d.ts.map