{"version": 3, "file": "CommandManager.js", "sourceRoot": "", "sources": ["../../src/commands/CommandManager.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;;;;AAEH,2CAAwC;AACxC,4CAAoB;AACpB,gDAAwB;AACxB,oDAAoG;AAEpG,2CAA8C;AAE9C,6BAA6B;AAC7B,6DAA0D;AAC1D,qDAAkD;AAClD,qDAAkD;AAClD,kEAA+D;AAC/D,kEAA+D;AAC/D,uDAAoD;AACpD,iEAA8D;AAC9D,qEAAkE;AAalE;;GAEG;AACH,MAAa,cAAc;IAMzB;QAFQ,uBAAkB,GAAQ,IAAI,CAAC;QAGrC,IAAI,CAAC,MAAM,GAAG,IAAA,qBAAY,EAAC,iBAAiB,CAAC,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,6BAAe,CAAC;QAChC,IAAI,CAAC,eAAe,GAAG,IAAI,uBAAU,EAAE,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,GAAQ;QAC5B,IAAI,CAAC,kBAAkB,GAAG,GAAG,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QAEzD,MAAM,KAAK,GAAqB;YAC9B,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,CAAC;YAClB,cAAc,EAAE,CAAC;YACjB,WAAW,EAAE,CAAC;YACd,UAAU,EAAE;gBACV,CAAC,6BAAe,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,CAAC,6BAAe,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1B,CAAC,6BAAe,CAAC,IAAI,CAAC,EAAE,CAAC;gBACzB,CAAC,6BAAe,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,CAAC,6BAAe,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,CAAC,6BAAe,CAAC,UAAU,CAAC,EAAE,CAAC;aAChC;SACF,CAAC;QAEF,iCAAiC;QACjC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QAElC,uBAAuB;QACvB,MAAM,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAErC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE;YAC5D,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,eAAe,EAAE,KAAK,CAAC,eAAe;YACtC,cAAc,EAAE,KAAK,CAAC,cAAc;YACpC,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,UAAU,EAAE,KAAK,CAAC,UAAU;SAC7B,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,KAAuB;QACnD,+CAA+C;QAC/C,MAAM,YAAY,GAAG,IAAI,2BAAY,EAAE,CAAC;QACxC,MAAM,iBAAiB,GAAG,IAAI,qCAAiB,EAAE,CAAC;QAClD,MAAM,mBAAmB,GAAG,IAAI,yCAAmB,EAAE,CAAC;QAEtD,sCAAsC;QACtC,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC5B,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;gBACxE,YAAY,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;gBAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC;gBAE9E,4CAA4C;gBAC5C,IAAI,CAAC;oBACH,MAAM,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;oBAC5E,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,wBAAwB,CAAC,CAAC;oBAEvF,iBAAiB,CAAC,gBAAgB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC;oBACjE,mBAAmB,CAAC,gBAAgB,CAAC,YAAY,EAAE,cAAc,EAAE,iBAAiB,CAAC,CAAC;oBAEtF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC;gBACpF,CAAC;gBAAC,OAAO,YAAY,EAAE,CAAC;oBACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0EAA0E,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;gBACxH,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kEAAkE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAClG,CAAC;QACH,CAAC;QAED,MAAM,WAAW,GAAkB;YACjC,IAAI,+BAAc,EAAE;YACpB,IAAI,uBAAU,EAAE;YAChB,IAAI,yBAAW,EAAE;YACjB,IAAI,uCAAkB,EAAE;YACxB,IAAI,uCAAkB,EAAE;YACxB,YAAY;YACZ,iBAAiB;YACjB,mBAAmB;SACpB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,WAAW,EAAE,CAAC;YAClC,IAAI,CAAC;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAErD,KAAK,CAAC,eAAe,EAAE,CAAC;gBACxB,KAAK,CAAC,WAAW,EAAE,CAAC;gBACpB,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,QAA2B,CAAC,EAAE,CAAC;gBAExD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACjF,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gDAAgD,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAClG,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,KAAuB;QACtD,MAAM,YAAY,GAAG,cAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;QAExD,0DAA0D;QAC1D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC;YACxB,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,SAAS;YACpB,gBAAgB,EAAE,gBAAgB;YAClC,gBAAgB,EAAE,gBAAgB;SACnC,CAAC,CAAC;QAEH,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;YAChC,IAAI,SAAS,CAAC,GAAG,CAAC,cAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;gBACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,IAAI,gCAAgC,CAAC,CAAC;gBACrF,SAAS;YACX,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;gBAE9B,IAAI,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBACpC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBAErD,KAAK,CAAC,cAAc,EAAE,CAAC;oBACvB,KAAK,CAAC,WAAW,EAAE,CAAC;oBAEpB,oCAAoC;oBACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjE,IAAI,QAAQ,EAAE,CAAC;wBACb,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAC/B,CAAC;oBAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;gBACpF,CAAC;qBAAM,CAAC;oBACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;gBACrE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mDAAmD,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBACxF,KAAK,CAAC,WAAW,EAAE,CAAC;YACtB,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,GAAW;QACjC,MAAM,KAAK,GAAa,EAAE,CAAC;QAE3B,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,YAAE,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;gBACtC,MAAM,IAAI,GAAG,YAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEnC,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;oBACvB,2BAA2B;oBAC3B,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;wBAC9F,SAAS;oBACX,CAAC;oBACD,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;gBAChD,CAAC;qBAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;oBACxD,qCAAqC;oBACrC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;wBACnF,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACvB,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QACnF,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,WAAmB;QACjD,MAAM,eAAe,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACnD,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,EAAE,gBAAgB,EAAE,aAAa,EAAE,mBAAmB,CAAC,CAAC;QACxG,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,CAAC,CAAC;QACjH,MAAM,iBAAiB,GAAG,CAAC,WAAW,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC;QACzE,MAAM,eAAe,GAAG,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QACjD,MAAM,kBAAkB,GAAG,CAAC,aAAa,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;QAEjF,IAAI,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,6BAAe,CAAC,OAAO,CAAC;QAC1E,IAAI,aAAa,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,6BAAe,CAAC,KAAK,CAAC;QACtE,IAAI,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,6BAAe,CAAC,IAAI,CAAC;QACpE,IAAI,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,6BAAe,CAAC,SAAS,CAAC;QAC9E,IAAI,eAAe,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,6BAAe,CAAC,OAAO,CAAC;QAC1E,IAAI,kBAAkB,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,6BAAe,CAAC,UAAU,CAAC;QAEhF,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAY;QACrB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACH,qBAAqB,CAAC,QAAyB;QAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI;YACxC,uBAAuB,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;YACjD,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBACpD,QAAQ,EAAE,GAAG;gBACb,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM;aAC/C,CAAC,CAAC;SACJ,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK;QACH,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;IAC7D,CAAC;CACF;AA9QD,wCA8QC;AAED;;GAEG;AACU,QAAA,cAAc,GAAG,IAAI,cAAc,EAAE,CAAC;AAEnD,kBAAe,cAAc,CAAC"}