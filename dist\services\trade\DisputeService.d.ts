/**
 * Dispute Service
 * Handles trade dispute resolution workflow
 */
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { IDisputeCase } from '../../models';
export interface DisputeCreationParams {
    tradeId: string;
    initiatorId: string;
    reason: string;
    description?: string;
    category: 'ITEM_NOT_RECEIVED' | 'ITEM_NOT_AS_DESCRIBED' | 'PAYMENT_ISSUE' | 'COMMUNICATION_ISSUE' | 'OTHER';
}
export interface DisputeResolutionParams {
    disputeId: string;
    adminId: string;
    resolution: 'FAVOR_INITIATOR' | 'FAVOR_RESPONDENT' | 'SPLIT_ESCROW' | 'FULL_REFUND' | 'CUSTOM';
    resolutionDetails: string;
    resolutionAmount?: number;
    adminNotes?: string;
}
/**
 * Dispute Service Class
 */
export declare class DisputeService extends BaseService {
    private escrowManager;
    private notificationManager;
    constructor(app: any);
    /**
     * Initialize the dispute service
     */
    onInitialize(): Promise<void>;
    /**
     * Initiate a dispute for a trade
     */
    initiateDispute(params: DisputeCreationParams, client?: Client): Promise<IDisputeCase>;
    /**
     * Resolve a dispute
     */
    resolveDispute(params: DisputeResolutionParams, client?: Client): Promise<IDisputeCase>;
    /**
     * Get dispute case by ID
     */
    getDispute(disputeId: string): Promise<IDisputeCase | null>;
    /**
     * Get active disputes for admin review
     */
    getActiveDisputes(guildId?: string, limit?: number): Promise<IDisputeCase[]>;
    /**
     * Add evidence to a dispute
     */
    addEvidence(disputeId: string, userId: string, evidence: string[]): Promise<IDisputeCase>;
    private validateDisputeInitiation;
    private validateDisputeResolution;
    private executeResolution;
    private calculateDisputePriority;
    private updateUserTradeStats;
    private generateDisputeId;
}
//# sourceMappingURL=DisputeService.d.ts.map