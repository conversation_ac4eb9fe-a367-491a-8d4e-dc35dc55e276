/**
 * Trade Validator
 * Handles validation logic for trade operations
 */

import { BaseService } from '../../base/BaseService';
import { ValidationError } from '../../../utils/errorHandler';
import { TRADE, VALIDATION } from '../../../config/constants';
import { 
  Trade, 
  ITrade, 
  UserTradeStats, 
  IUserTradeStats,
  User
} from '../../../models';
import { TradeCreationParams } from '../TradeService';

/**
 * Trade Validator Class
 */
export class TradeValidator extends BaseService {
  public readonly name = 'TradeValidator';

  constructor(app: any) {
    super(app);
  }

  /**
   * Initialize the trade validator
   */
  async initialize(): Promise<void> {
    this.logger.info('[TradeValidator] Trade validator initialized');
  }

  /**
   * Validate trade creation parameters
   */
  async validateTradeCreation(params: TradeCreationParams): Promise<void> {
    this.logOperation('Validating trade creation', params);

    try {
      // Basic parameter validation
      this.validateBasicTradeParams(params);

      // Validate users exist and are different
      await this.validateTradeParties(params.sellerId, params.buyerId);

      // Check for existing active trades between parties
      await this.validateNoActiveTradeBetweenParties(params.sellerId, params.buyerId, params.guildId);

      // Validate user trade limits and restrictions
      await this.validateUserTradeEligibility(params.sellerId, params.guildId);
      await this.validateUserTradeEligibility(params.buyerId, params.guildId);

      // Validate buyer has sufficient balance (if buyer initiated)
      if (params.initiatedBy === 'BUYER') {
        await this.validateBuyerBalance(params.buyerId, params.amount);
      }

      this.logOperation('Trade creation validation passed', { 
        sellerId: params.sellerId, 
        buyerId: params.buyerId,
        amount: params.amount 
      });

    } catch (error) {
      this.handleError(error, { operation: 'validate_trade_creation', params });
      throw error;
    }
  }

  /**
   * Validate trade acceptance
   */
  async validateTradeAcceptance(trade: ITrade, acceptingUserId: string): Promise<void> {
    this.logOperation('Validating trade acceptance', { 
      tradeId: trade.tradeId, 
      acceptingUserId 
    });

    try {
      // Check trade state
      if (trade.state !== TRADE.STATES.PROPOSED) {
        throw new ValidationError(`Trade is not in PROPOSED state. Current state: ${trade.state}`);
      }

      // Check if trade is expired
      if (trade.isExpired()) {
        throw new ValidationError('Trade has expired and cannot be accepted');
      }

      // Validate accepting user is involved in trade
      if (!trade.involvesUser(acceptingUserId)) {
        throw new ValidationError('You are not a party to this trade');
      }

      // Check if user is trying to accept their own proposal
      const isInitiator = (trade.initiatedBy === 'SELLER' && trade.sellerId === acceptingUserId) ||
                         (trade.initiatedBy === 'BUYER' && trade.buyerId === acceptingUserId);
      
      if (isInitiator) {
        throw new ValidationError('You cannot accept your own trade proposal');
      }

      // Validate buyer has sufficient balance
      await this.validateBuyerBalance(trade.buyerId, trade.amount);

      // Check user trade eligibility
      await this.validateUserTradeEligibility(acceptingUserId, trade.guildId);

      this.logOperation('Trade acceptance validation passed', { 
        tradeId: trade.tradeId, 
        acceptingUserId 
      });

    } catch (error) {
      this.handleError(error, { operation: 'validate_trade_acceptance', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Validate trade confirmation
   */
  async validateTradeConfirmation(trade: ITrade, confirmingUserId: string): Promise<void> {
    this.logOperation('Validating trade confirmation', { 
      tradeId: trade.tradeId, 
      confirmingUserId 
    });

    try {
      // Check trade state
      if (trade.state !== TRADE.STATES.ACTIVE) {
        throw new ValidationError(`Trade is not in ACTIVE state. Current state: ${trade.state}`);
      }

      // Check if trade is expired
      if (trade.isExpired()) {
        throw new ValidationError('Trade has expired and cannot be confirmed');
      }

      // Validate confirming user is involved in trade
      if (!trade.involvesUser(confirmingUserId)) {
        throw new ValidationError('You are not a party to this trade');
      }

      // Check if user already confirmed
      if ((trade.sellerId === confirmingUserId && trade.sellerConfirmed) ||
          (trade.buyerId === confirmingUserId && trade.buyerConfirmed)) {
        throw new ValidationError('You have already confirmed this trade');
      }

      this.logOperation('Trade confirmation validation passed', { 
        tradeId: trade.tradeId, 
        confirmingUserId 
      });

    } catch (error) {
      this.handleError(error, { operation: 'validate_trade_confirmation', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Validate trade cancellation
   */
  async validateTradeCancellation(trade: ITrade, cancellingUserId: string): Promise<void> {
    this.logOperation('Validating trade cancellation', { 
      tradeId: trade.tradeId, 
      cancellingUserId 
    });

    try {
      // Check trade state - can only cancel PROPOSED, ACCEPTED, or ACTIVE trades
      const cancellableStates = [TRADE.STATES.PROPOSED, TRADE.STATES.ACCEPTED, TRADE.STATES.ACTIVE];
      if (!cancellableStates.includes(trade.state as any)) {
        throw new ValidationError(`Trade cannot be cancelled in ${trade.state} state`);
      }

      // Validate cancelling user is involved in trade
      if (!trade.involvesUser(cancellingUserId)) {
        throw new ValidationError('You are not a party to this trade');
      }

      // Additional restrictions for ACTIVE trades
      if (trade.state === TRADE.STATES.ACTIVE) {
        // Check if both parties have confirmed (should not allow cancellation)
        if (trade.sellerConfirmed && trade.buyerConfirmed) {
          throw new ValidationError('Trade cannot be cancelled after both parties have confirmed completion');
        }
      }

      this.logOperation('Trade cancellation validation passed', { 
        tradeId: trade.tradeId, 
        cancellingUserId 
      });

    } catch (error) {
      this.handleError(error, { operation: 'validate_trade_cancellation', tradeId: trade.tradeId });
      throw error;
    }
  }

  /**
   * Validate dispute initiation
   */
  async validateDisputeInitiation(trade: ITrade, disputingUserId: string): Promise<void> {
    this.logOperation('Validating dispute initiation', { 
      tradeId: trade.tradeId, 
      disputingUserId 
    });

    try {
      // Check trade state - can only dispute ACTIVE trades
      if (trade.state !== TRADE.STATES.ACTIVE) {
        throw new ValidationError(`Disputes can only be initiated for ACTIVE trades. Current state: ${trade.state}`);
      }

      // Validate disputing user is involved in trade
      if (!trade.involvesUser(disputingUserId)) {
        throw new ValidationError('You are not a party to this trade');
      }

      // Check if trade already has a dispute
      if (trade.disputeId) {
        throw new ValidationError('This trade already has an active dispute');
      }

      // Check if trade is near expiration (might be better to let it expire)
      const hoursUntilExpiration = (trade.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60);
      if (hoursUntilExpiration < 1) {
        throw new ValidationError('Trade is too close to expiration. Please wait for automatic expiration.');
      }

      this.logOperation('Dispute initiation validation passed', { 
        tradeId: trade.tradeId, 
        disputingUserId 
      });

    } catch (error) {
      this.handleError(error, { operation: 'validate_dispute_initiation', tradeId: trade.tradeId });
      throw error;
    }
  }

  // Private validation methods

  private validateBasicTradeParams(params: TradeCreationParams): void {
    // Validate Discord IDs
    if (!VALIDATION.DISCORD_ID_REGEX.test(params.sellerId)) {
      throw new ValidationError('Invalid seller Discord ID');
    }
    if (!VALIDATION.DISCORD_ID_REGEX.test(params.buyerId)) {
      throw new ValidationError('Invalid buyer Discord ID');
    }
    if (!VALIDATION.DISCORD_ID_REGEX.test(params.guildId)) {
      throw new ValidationError('Invalid guild Discord ID');
    }

    // Validate amount
    if (!Number.isInteger(params.amount) || params.amount < TRADE.MIN_TRADE_AMOUNT) {
      throw new ValidationError(`Trade amount must be at least ${TRADE.MIN_TRADE_AMOUNT} PLC`);
    }
    if (params.amount > TRADE.MAX_TRADE_AMOUNT) {
      throw new ValidationError(`Trade amount cannot exceed ${TRADE.MAX_TRADE_AMOUNT} PLC`);
    }

    // Validate item description
    if (!params.itemDescription || params.itemDescription.trim().length === 0) {
      throw new ValidationError('Item description is required');
    }
    if (params.itemDescription.length > VALIDATION.MAX_DESCRIPTION_LENGTH) {
      throw new ValidationError(`Item description cannot exceed ${VALIDATION.MAX_DESCRIPTION_LENGTH} characters`);
    }

    // Validate notes if provided
    if (params.notes && params.notes.length > VALIDATION.MAX_REASON_LENGTH) {
      throw new ValidationError(`Notes cannot exceed ${VALIDATION.MAX_REASON_LENGTH} characters`);
    }

    // Validate initiatedBy
    if (!['SELLER', 'BUYER'].includes(params.initiatedBy)) {
      throw new ValidationError('Invalid initiator type');
    }
  }

  private async validateTradeParties(sellerId: string, buyerId: string): Promise<void> {
    // Check users are different
    if (sellerId === buyerId) {
      throw new ValidationError('You cannot trade with yourself');
    }

    // Ensure both users exist in the database
    const seller = await User.findOne({ discordId: sellerId });
    const buyer = await User.findOne({ discordId: buyerId });

    if (!seller) {
      throw new ValidationError('Seller not found in the system');
    }
    if (!buyer) {
      throw new ValidationError('Buyer not found in the system');
    }
  }

  private async validateNoActiveTradeBetweenParties(
    sellerId: string, 
    buyerId: string, 
    guildId: string
  ): Promise<void> {
    const existingTrade = await Trade.findOne({
      guildId,
      $or: [
        { sellerId, buyerId },
        { sellerId: buyerId, buyerId: sellerId }
      ],
      state: { $in: [TRADE.STATES.PROPOSED, TRADE.STATES.ACCEPTED, TRADE.STATES.ACTIVE] }
    });

    if (existingTrade) {
      throw new ValidationError('You already have an active trade with this user');
    }
  }

  private async validateUserTradeEligibility(discordId: string, guildId: string): Promise<void> {
    // Get or create user trade stats
    let userStats = await UserTradeStats.findOne({ discordId, guildId });
    
    if (!userStats) {
      // Create default stats for new user
      userStats = new UserTradeStats({
        discordId,
        guildId,
        totalTrades: 0,
        successfulTrades: 0,
        cancelledTrades: 0,
        expiredTrades: 0,
        disputedTrades: 0,
        tradesAsSeller: 0,
        tradesAsBuyer: 0,
        totalVolumeTraded: 0,
        averageTradeValue: 0,
        largestTrade: 0,
        reputationScore: 50,
        disputeRatio: 0,
        completionRate: 0,
        averageCompletionTime: 0,
        fastestCompletion: 0,
        activeTrades: 0,
        isRestricted: false,
        dailyTradeCount: 0,
        lastTradeDate: new Date(),
        lastResetDate: new Date(),
        lastUpdated: new Date(),
        warningsReceived: 0,
        violationHistory: []
      });
      await userStats.save();
    }

    // Check if user can trade
    const eligibility = userStats.canTrade();
    if (!eligibility.eligible) {
      throw new ValidationError(eligibility.reason || 'User is not eligible to trade');
    }
  }

  private async validateBuyerBalance(buyerId: string, amount: number): Promise<void> {
    const buyer = await User.findOne({ discordId: buyerId });
    if (!buyer) {
      throw new ValidationError('Buyer not found');
    }

    if (buyer.balance < amount) {
      throw new ValidationError(
        `Insufficient balance. Required: ${amount} PLC, Available: ${buyer.balance} PLC`
      );
    }
  }
}
