/**
 * Auto-Deployment Utilities
 *
 * This module provides functions for automating build and deployment processes
 * that would normally be run manually via npm scripts.
 *
 * These utilities allow the bot to self-build and self-deploy when running on Discloud
 * without requiring manual intervention.
 */
/**
 * Check if we're running in a production environment (like Discloud)
 */
export declare function isProductionEnvironment(): boolean;
/**
 * Check if TypeScript source files exist but compiled JavaScript files don't
 * This indicates we need to run the build process
 */
export declare function needsBuild(): Promise<boolean>;
/**
 * Run the TypeScript compiler to build the project
 */
export declare function runBuild(): Promise<boolean>;
/**
 * Deploy slash commands to Discord API
 * This is a programmatic version of the deploy-commands.ts script
 */
export declare function deployCommands(): Promise<boolean>;
/**
 * Deploy role commands to Discord API
 * This is a programmatic version of the deploy-role-commands.ts script
 */
export declare function deployRoleCommands(): Promise<boolean>;
/**
 * Validate environment variables required for deployment
 */
export declare function validateEnvironment(): {
    valid: boolean;
    missing: string[];
};
/**
 * Check if all required dependencies are available
 */
export declare function checkDependencies(): Promise<{
    valid: boolean;
    issues: string[];
}>;
//# sourceMappingURL=auto-deployment.d.ts.map