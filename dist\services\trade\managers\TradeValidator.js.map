{"version": 3, "file": "TradeValidator.js", "sourceRoot": "", "sources": ["../../../../src/services/trade/managers/TradeValidator.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,wDAAqD;AACrD,8DAA8D;AAC9D,yDAA8D;AAC9D,4CAMyB;AAGzB;;GAEG;AACH,MAAa,cAAe,SAAQ,yBAAW;IAC7C,YAAY,GAAQ;QAClB,KAAK,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8CAA8C,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,MAA2B;QACrD,IAAI,CAAC,YAAY,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;QAEvD,IAAI,CAAC;YACH,6BAA6B;YAC7B,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,CAAC;YAEtC,yCAAyC;YACzC,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAEjE,mDAAmD;YACnD,MAAM,IAAI,CAAC,mCAAmC,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAEhG,8CAA8C;YAC9C,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YACzE,MAAM,IAAI,CAAC,4BAA4B,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;YAExE,6DAA6D;YAC7D,IAAI,MAAM,CAAC,WAAW,KAAK,OAAO,EAAE,CAAC;gBACnC,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACjE,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,kCAAkC,EAAE;gBACpD,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,yBAAyB,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,KAAa,EAAE,eAAuB;QAClE,IAAI,CAAC,YAAY,CAAC,6BAA6B,EAAE;YAC/C,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,eAAe;SAChB,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,oBAAoB;YACpB,IAAI,KAAK,CAAC,KAAK,KAAK,iBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAC1C,MAAM,IAAI,8BAAe,CAAC,kDAAkD,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC7F,CAAC;YAED,4BAA4B;YAC5B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC;gBACtB,MAAM,IAAI,8BAAe,CAAC,0CAA0C,CAAC,CAAC;YACxE,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,8BAAe,CAAC,mCAAmC,CAAC,CAAC;YACjE,CAAC;YAED,uDAAuD;YACvD,MAAM,WAAW,GAAG,CAAC,KAAK,CAAC,WAAW,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK,eAAe,CAAC;gBACvE,CAAC,KAAK,CAAC,WAAW,KAAK,OAAO,IAAI,KAAK,CAAC,OAAO,KAAK,eAAe,CAAC,CAAC;YAExF,IAAI,WAAW,EAAE,CAAC;gBAChB,MAAM,IAAI,8BAAe,CAAC,2CAA2C,CAAC,CAAC;YACzE,CAAC;YAED,wCAAwC;YACxC,MAAM,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;YAE7D,+BAA+B;YAC/B,MAAM,IAAI,CAAC,4BAA4B,CAAC,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAExE,IAAI,CAAC,YAAY,CAAC,oCAAoC,EAAE;gBACtD,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,eAAe;aAChB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,2BAA2B,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC5F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,KAAa,EAAE,gBAAwB;QACrE,IAAI,CAAC,YAAY,CAAC,+BAA+B,EAAE;YACjD,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,gBAAgB;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,oBAAoB;YACpB,IAAI,KAAK,CAAC,KAAK,KAAK,iBAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxC,MAAM,IAAI,8BAAe,CAAC,gDAAgD,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC3F,CAAC;YAED,4BAA4B;YAC5B,IAAI,KAAK,CAAC,SAAS,EAAE,EAAE,CAAC;gBACtB,MAAM,IAAI,8BAAe,CAAC,2CAA2C,CAAC,CAAC;YACzE,CAAC;YAED,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,8BAAe,CAAC,mCAAmC,CAAC,CAAC;YACjE,CAAC;YAED,kCAAkC;YAClC,IAAI,CAAC,KAAK,CAAC,QAAQ,KAAK,gBAAgB,IAAI,KAAK,CAAC,eAAe,CAAC;gBAC9D,CAAC,KAAK,CAAC,OAAO,KAAK,gBAAgB,IAAI,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;gBACjE,MAAM,IAAI,8BAAe,CAAC,uCAAuC,CAAC,CAAC;YACrE,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,sCAAsC,EAAE;gBACxD,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,gBAAgB;aACjB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,6BAA6B,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,KAAa,EAAE,gBAAwB;QACrE,IAAI,CAAC,YAAY,CAAC,+BAA+B,EAAE;YACjD,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,gBAAgB;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,2EAA2E;YAC3E,MAAM,iBAAiB,GAAG,CAAC,iBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9F,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAY,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,8BAAe,CAAC,gCAAgC,KAAK,CAAC,KAAK,QAAQ,CAAC,CAAC;YACjF,CAAC;YAED,gDAAgD;YAChD,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,8BAAe,CAAC,mCAAmC,CAAC,CAAC;YACjE,CAAC;YAED,4CAA4C;YAC5C,IAAI,KAAK,CAAC,KAAK,KAAK,iBAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxC,uEAAuE;gBACvE,IAAI,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,cAAc,EAAE,CAAC;oBAClD,MAAM,IAAI,8BAAe,CAAC,wEAAwE,CAAC,CAAC;gBACtG,CAAC;YACH,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,sCAAsC,EAAE;gBACxD,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,gBAAgB;aACjB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,6BAA6B,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,yBAAyB,CAAC,KAAa,EAAE,eAAuB;QACpE,IAAI,CAAC,YAAY,CAAC,+BAA+B,EAAE;YACjD,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,eAAe;SAChB,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,qDAAqD;YACrD,IAAI,KAAK,CAAC,KAAK,KAAK,iBAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;gBACxC,MAAM,IAAI,8BAAe,CAAC,oEAAoE,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAC/G,CAAC;YAED,+CAA+C;YAC/C,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,EAAE,CAAC;gBACzC,MAAM,IAAI,8BAAe,CAAC,mCAAmC,CAAC,CAAC;YACjE,CAAC;YAED,uCAAuC;YACvC,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;gBACpB,MAAM,IAAI,8BAAe,CAAC,0CAA0C,CAAC,CAAC;YACxE,CAAC;YAED,uEAAuE;YACvE,MAAM,oBAAoB,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YACzF,IAAI,oBAAoB,GAAG,CAAC,EAAE,CAAC;gBAC7B,MAAM,IAAI,8BAAe,CAAC,yEAAyE,CAAC,CAAC;YACvG,CAAC;YAED,IAAI,CAAC,YAAY,CAAC,sCAAsC,EAAE;gBACxD,OAAO,EAAE,KAAK,CAAC,OAAO;gBACtB,eAAe;aAChB,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,SAAS,EAAE,6BAA6B,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9F,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,6BAA6B;IAErB,wBAAwB,CAAC,MAA2B;QAC1D,uBAAuB;QACvB,IAAI,CAAC,sBAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,8BAAe,CAAC,2BAA2B,CAAC,CAAC;QACzD,CAAC;QACD,IAAI,CAAC,sBAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,8BAAe,CAAC,0BAA0B,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,CAAC,sBAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,8BAAe,CAAC,0BAA0B,CAAC,CAAC;QACxD,CAAC;QAED,kBAAkB;QAClB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAK,CAAC,gBAAgB,EAAE,CAAC;YAC/E,MAAM,IAAI,8BAAe,CAAC,iCAAiC,iBAAK,CAAC,gBAAgB,MAAM,CAAC,CAAC;QAC3F,CAAC;QACD,IAAI,MAAM,CAAC,MAAM,GAAG,iBAAK,CAAC,gBAAgB,EAAE,CAAC;YAC3C,MAAM,IAAI,8BAAe,CAAC,8BAA8B,iBAAK,CAAC,gBAAgB,MAAM,CAAC,CAAC;QACxF,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1E,MAAM,IAAI,8BAAe,CAAC,8BAA8B,CAAC,CAAC;QAC5D,CAAC;QACD,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,sBAAU,CAAC,sBAAsB,EAAE,CAAC;YACtE,MAAM,IAAI,8BAAe,CAAC,kCAAkC,sBAAU,CAAC,sBAAsB,aAAa,CAAC,CAAC;QAC9G,CAAC;QAED,6BAA6B;QAC7B,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,sBAAU,CAAC,iBAAiB,EAAE,CAAC;YACvE,MAAM,IAAI,8BAAe,CAAC,uBAAuB,sBAAU,CAAC,iBAAiB,aAAa,CAAC,CAAC;QAC9F,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC;YACtD,MAAM,IAAI,8BAAe,CAAC,wBAAwB,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,QAAgB,EAAE,OAAe;QAClE,4BAA4B;QAC5B,IAAI,QAAQ,KAAK,OAAO,EAAE,CAAC;YACzB,MAAM,IAAI,8BAAe,CAAC,gCAAgC,CAAC,CAAC;QAC9D,CAAC;QAED,0CAA0C;QAC1C,MAAM,MAAM,GAAG,MAAM,aAAI,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC3D,MAAM,KAAK,GAAG,MAAM,aAAI,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;QAEzD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,8BAAe,CAAC,gCAAgC,CAAC,CAAC;QAC9D,CAAC;QACD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,8BAAe,CAAC,+BAA+B,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mCAAmC,CAC/C,QAAgB,EAChB,OAAe,EACf,OAAe;QAEf,MAAM,aAAa,GAAG,MAAM,cAAK,CAAC,OAAO,CAAC;YACxC,OAAO;YACP,GAAG,EAAE;gBACH,EAAE,QAAQ,EAAE,OAAO,EAAE;gBACrB,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;aACzC;YACD,KAAK,EAAE,EAAE,GAAG,EAAE,CAAC,iBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAK,CAAC,MAAM,CAAC,QAAQ,EAAE,iBAAK,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;SACpF,CAAC,CAAC;QAEH,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,IAAI,8BAAe,CAAC,iDAAiD,CAAC,CAAC;QAC/E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,4BAA4B,CAAC,SAAiB,EAAE,OAAe;QAC3E,iCAAiC;QACjC,IAAI,SAAS,GAAG,MAAM,uBAAc,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;QAErE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,oCAAoC;YACpC,SAAS,GAAG,IAAI,uBAAc,CAAC;gBAC7B,SAAS;gBACT,OAAO;gBACP,WAAW,EAAE,CAAC;gBACd,gBAAgB,EAAE,CAAC;gBACnB,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC;gBACjB,cAAc,EAAE,CAAC;gBACjB,aAAa,EAAE,CAAC;gBAChB,iBAAiB,EAAE,CAAC;gBACpB,iBAAiB,EAAE,CAAC;gBACpB,YAAY,EAAE,CAAC;gBACf,eAAe,EAAE,EAAE;gBACnB,YAAY,EAAE,CAAC;gBACf,cAAc,EAAE,CAAC;gBACjB,qBAAqB,EAAE,CAAC;gBACxB,iBAAiB,EAAE,CAAC;gBACpB,YAAY,EAAE,CAAC;gBACf,YAAY,EAAE,KAAK;gBACnB,eAAe,EAAE,CAAC;gBAClB,aAAa,EAAE,IAAI,IAAI,EAAE;gBACzB,aAAa,EAAE,IAAI,IAAI,EAAE;gBACzB,WAAW,EAAE,IAAI,IAAI,EAAE;gBACvB,gBAAgB,EAAE,CAAC;gBACnB,gBAAgB,EAAE,EAAE;aACrB,CAAC,CAAC;YACH,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;QACzB,CAAC;QAED,0BAA0B;QAC1B,MAAM,WAAW,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,IAAI,8BAAe,CAAC,WAAW,CAAC,MAAM,IAAI,+BAA+B,CAAC,CAAC;QACnF,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,MAAc;QAChE,MAAM,KAAK,GAAG,MAAM,aAAI,CAAC,OAAO,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAAC;QACzD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,MAAM,IAAI,8BAAe,CAAC,iBAAiB,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,KAAK,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAI,8BAAe,CACvB,mCAAmC,MAAM,oBAAoB,KAAK,CAAC,OAAO,MAAM,CACjF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AApWD,wCAoWC"}