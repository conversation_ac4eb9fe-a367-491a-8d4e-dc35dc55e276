"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = require("mongoose");
const constants_1 = require("../config/constants");
const userTradeStatsSchema = new mongoose_1.Schema({
    discordId: {
        type: String,
        required: [true, 'Discord ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Discord ID must be a valid Discord snowflake'
        },
        index: true
    },
    guildId: {
        type: String,
        required: [true, 'Guild ID is required'],
        validate: {
            validator: function (v) {
                return /^\d{17,20}$/.test(v);
            },
            message: 'Guild ID must be a valid Discord snowflake'
        },
        index: true
    },
    totalTrades: {
        type: Number,
        default: 0,
        min: [0, 'Total trades cannot be negative']
    },
    successfulTrades: {
        type: Number,
        default: 0,
        min: [0, 'Successful trades cannot be negative']
    },
    cancelledTrades: {
        type: Number,
        default: 0,
        min: [0, 'Cancelled trades cannot be negative']
    },
    expiredTrades: {
        type: Number,
        default: 0,
        min: [0, 'Expired trades cannot be negative']
    },
    disputedTrades: {
        type: Number,
        default: 0,
        min: [0, 'Disputed trades cannot be negative']
    },
    tradesAsSeller: {
        type: Number,
        default: 0,
        min: [0, 'Trades as seller cannot be negative']
    },
    tradesAsBuyer: {
        type: Number,
        default: 0,
        min: [0, 'Trades as buyer cannot be negative']
    },
    totalVolumeTraded: {
        type: Number,
        default: 0,
        min: [0, 'Total volume traded cannot be negative']
    },
    averageTradeValue: {
        type: Number,
        default: 0,
        min: [0, 'Average trade value cannot be negative']
    },
    largestTrade: {
        type: Number,
        default: 0,
        min: [0, 'Largest trade cannot be negative']
    },
    reputationScore: {
        type: Number,
        default: 50, // Start with neutral reputation
        min: [0, 'Reputation score cannot be below 0'],
        max: [100, 'Reputation score cannot be above 100'],
        index: true
    },
    disputeRatio: {
        type: Number,
        default: 0,
        min: [0, 'Dispute ratio cannot be negative'],
        max: [1, 'Dispute ratio cannot be above 1'],
        index: true
    },
    completionRate: {
        type: Number,
        default: 0,
        min: [0, 'Completion rate cannot be negative'],
        max: [1, 'Completion rate cannot be above 1']
    },
    averageCompletionTime: {
        type: Number,
        default: 0,
        min: [0, 'Average completion time cannot be negative']
    },
    fastestCompletion: {
        type: Number,
        default: 0,
        min: [0, 'Fastest completion cannot be negative']
    },
    activeTrades: {
        type: Number,
        default: 0,
        min: [0, 'Active trades cannot be negative'],
        max: [constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER, `Cannot have more than ${constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER} active trades`],
        index: true
    },
    isRestricted: {
        type: Boolean,
        default: false,
        index: true
    },
    restrictionReason: {
        type: String,
        maxlength: [200, 'Restriction reason cannot exceed 200 characters']
    },
    restrictedUntil: {
        type: Date,
        index: true
    },
    dailyTradeCount: {
        type: Number,
        default: 0,
        min: [0, 'Daily trade count cannot be negative'],
        max: [constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY, `Cannot exceed ${constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY} trades per day`]
    },
    lastTradeDate: {
        type: Date,
        default: Date.now,
        index: true
    },
    lastResetDate: {
        type: Date,
        default: Date.now
    },
    firstTradeDate: {
        type: Date
    },
    lastUpdated: {
        type: Date,
        default: Date.now,
        index: true
    },
    warningsReceived: {
        type: Number,
        default: 0,
        min: [0, 'Warnings received cannot be negative']
    },
    lastWarningDate: {
        type: Date
    },
    violationHistory: {
        type: [String],
        default: [],
        validate: {
            validator: function (v) {
                return v.length <= 50; // Max 50 violation records
            },
            message: 'Cannot have more than 50 violation records'
        }
    }
}, {
    timestamps: false // Using custom timestamp fields
});
// Compound indexes for efficient queries
userTradeStatsSchema.index({ discordId: 1, guildId: 1 }, { unique: true });
userTradeStatsSchema.index({ guildId: 1, reputationScore: -1 });
userTradeStatsSchema.index({ guildId: 1, totalVolumeTraded: -1 });
userTradeStatsSchema.index({ isRestricted: 1, restrictedUntil: 1 });
// Update lastUpdated on save
userTradeStatsSchema.pre('save', function (next) {
    this.lastUpdated = new Date();
    next();
});
// Method to check if user can trade
userTradeStatsSchema.methods.canTrade = function () {
    // Check if restricted
    if (this.isRestricted) {
        if (this.restrictedUntil && new Date() > this.restrictedUntil) {
            // Restriction expired, remove it
            this.isRestricted = false;
            this.restrictedUntil = undefined;
            this.restrictionReason = undefined;
        }
        else {
            return {
                canTrade: false,
                reason: this.restrictionReason || 'Account is restricted from trading'
            };
        }
    }
    // Check daily trade limit
    this.resetDailyCountIfNeeded();
    if (this.dailyTradeCount >= constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY) {
        return {
            canTrade: false,
            reason: `Daily trade limit of ${constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY} reached`
        };
    }
    // Check active trade limit
    if (this.activeTrades >= constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER) {
        return {
            canTrade: false,
            reason: `Maximum of ${constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER} active trades allowed`
        };
    }
    return { canTrade: true };
};
// Method to reset daily count if needed
userTradeStatsSchema.methods.resetDailyCountIfNeeded = function () {
    const now = new Date();
    const lastReset = new Date(this.lastResetDate);
    // Check if it's a new day
    if (now.getDate() !== lastReset.getDate() ||
        now.getMonth() !== lastReset.getMonth() ||
        now.getFullYear() !== lastReset.getFullYear()) {
        this.dailyTradeCount = 0;
        this.lastResetDate = now;
    }
};
// Method to calculate reputation score
userTradeStatsSchema.methods.calculateReputationScore = function () {
    if (this.totalTrades < constants_1.TRADE.MIN_TRADES_FOR_REPUTATION) {
        return 50; // Neutral score for new users
    }
    let score = 50; // Start with neutral
    // Positive factors
    score += this.completionRate * 30; // Up to +30 for 100% completion rate
    score += Math.min(this.totalTrades / 10, 10); // Up to +10 for trade volume
    // Negative factors
    score -= this.disputeRatio * 40; // Up to -40 for high dispute ratio
    score -= this.warningsReceived * 2; // -2 per warning
    // Clamp between 0 and 100
    return Math.max(0, Math.min(100, Math.round(score)));
};
// Method to update stats after trade completion
userTradeStatsSchema.methods.updateAfterTrade = function (tradeAmount, completionTimeHours, wasSuccessful, wasDisputed, asSeller) {
    this.totalTrades++;
    this.totalVolumeTraded += tradeAmount;
    this.activeTrades = Math.max(0, this.activeTrades - 1);
    if (asSeller) {
        this.tradesAsSeller++;
    }
    else {
        this.tradesAsBuyer++;
    }
    if (wasSuccessful) {
        this.successfulTrades++;
        // Update timing stats
        if (this.averageCompletionTime === 0) {
            this.averageCompletionTime = completionTimeHours;
        }
        else {
            this.averageCompletionTime = (this.averageCompletionTime + completionTimeHours) / 2;
        }
        if (this.fastestCompletion === 0 || completionTimeHours < this.fastestCompletion) {
            this.fastestCompletion = completionTimeHours;
        }
    }
    if (wasDisputed) {
        this.disputedTrades++;
    }
    // Update largest trade
    if (tradeAmount > this.largestTrade) {
        this.largestTrade = tradeAmount;
    }
    // Recalculate derived stats
    this.averageTradeValue = this.totalVolumeTraded / this.totalTrades;
    this.disputeRatio = this.disputedTrades / this.totalTrades;
    this.completionRate = this.successfulTrades / this.totalTrades;
    this.reputationScore = this.calculateReputationScore();
    // Set first trade date if not set
    if (!this.firstTradeDate) {
        this.firstTradeDate = new Date();
    }
    this.lastTradeDate = new Date();
};
exports.default = (0, mongoose_1.model)('UserTradeStats', userTradeStatsSchema);
//# sourceMappingURL=UserTradeStats.js.map