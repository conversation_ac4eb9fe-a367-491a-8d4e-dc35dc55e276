/**
 * Interaction Create Event Handler
 * Handles Discord interaction events (commands and buttons)
 */
import { Interaction } from 'discord.js';
import { BaseEventHandler } from './base';
import { IApplicationContext } from '../core/interfaces';
/**
 * Interaction create event handler
 */
export declare class InteractionCreateEventHandler extends BaseEventHandler {
    readonly name = "interactionCreate";
    constructor(app: IApplicationContext);
    /**
     * Execute interaction create event
     */
    execute(interaction: Interaction): Promise<void>;
    /**
     * Handle chat input command interactions
     */
    private handleChatInputCommand;
    /**
     * Handle button interactions
     */
    private handleButtonInteraction;
    /**
     * Handle quick action buttons
     */
    private handleQuickAction;
    /**
     * Handle role achievement info buttons
     */
    private handleRoleAchievementInfo;
    /**
     * Handle announcement confirmation
     */
    private handleAnnouncementConfirm;
    /**
     * Handle announcement cancellation
     */
    private handleAnnouncementCancel;
    /**
     * Handle help command buttons
     */
    private handleHelpButton;
    /**
     * Handle trade button interactions
     */
    private handleTradeButton;
    private handleTradeAccept;
    private handleTradeDecline;
    private handleTradeConfirm;
    private handleTradeDispute;
    private handleTradeCancel;
    private handleTradeDetails;
}
//# sourceMappingURL=interactionCreate.d.ts.map