/**
 * Escrow Manager
 * Handles secure escrow operations for trades
 */
import mongoose from 'mongoose';
import { BaseService } from '../../base/BaseService';
import { IEscrowTransaction, ITrade } from '../../../models';
export interface EscrowOperation {
    tradeId: string;
    discordId: string;
    amount: number;
    type: 'LOCK' | 'RELEASE' | 'REFUND' | 'DISPUTE_HOLD';
    reason: string;
}
/**
 * Escrow Manager Class
 */
export declare class EscrowManager extends BaseService {
    constructor(app: any);
    /**
     * Initialize the escrow manager
     */
    initialize(): Promise<void>;
    /**
     * Lock funds in escrow for a trade
     */
    lockEscrow(trade: ITrade, session: mongoose.ClientSession): Promise<IEscrowTransaction>;
    /**
     * Release escrow funds to seller
     */
    releaseEscrow(trade: ITrade, reason: string, session: mongoose.ClientSession): Promise<IEscrowTransaction>;
    /**
     * Refund escrow funds to buyer
     */
    refundEscrow(trade: ITrade, reason: string, session: mongoose.ClientSession): Promise<IEscrowTransaction>;
    /**
     * Split escrow funds between parties (for dispute resolution)
     */
    splitEscrow(trade: ITrade, sellerAmount: number, buyerAmount: number, reason: string, session: mongoose.ClientSession): Promise<{
        sellerTransaction: IEscrowTransaction;
        buyerTransaction?: IEscrowTransaction;
    }>;
    /**
     * Get escrow balance for a trade
     */
    getEscrowBalance(tradeId: string): Promise<number>;
    /**
     * Get user's total escrowed amount
     */
    getUserEscrowedAmount(discordId: string): Promise<number>;
    private generateEscrowId;
    private generateTransactionId;
}
//# sourceMappingURL=EscrowManager.d.ts.map