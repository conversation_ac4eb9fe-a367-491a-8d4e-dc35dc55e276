"use strict";
/**
 * Trade Embed Builder
 * Specialized embed builders for trade system UI components
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTradeProposalEmbed = createTradeProposalEmbed;
exports.createTradeProposalButtons = createTradeProposalButtons;
exports.createActiveTradeEmbed = createActiveTradeEmbed;
exports.createActiveTradeButtons = createActiveTradeButtons;
exports.createCompletedTradeEmbed = createCompletedTradeEmbed;
exports.createCancelledTradeEmbed = createCancelledTradeEmbed;
exports.createUserTradeStatsEmbed = createUserTradeStatsEmbed;
exports.getTradeStateColor = getTradeStateColor;
exports.getTradeStateEmoji = getTradeStateEmoji;
const discord_js_1 = require("discord.js");
const embedBuilder_1 = require("./embedBuilder");
const constants_1 = require("../config/constants");
/**
 * Create a trade proposal embed
 */
function createTradeProposalEmbed(trade, seller, buyer) {
    const isSellerInitiated = trade.initiatedBy === 'SELLER';
    const initiator = isSellerInitiated ? seller : buyer;
    const recipient = isSellerInitiated ? buyer : seller;
    const embed = (0, embedBuilder_1.createBaseEmbed)(`${embedBuilder_1.EMOJIS.TRADE.PROPOSAL} Trade Proposal`, undefined, embedBuilder_1.COLORS.WARNING);
    embed.addFields([
        {
            name: `${embedBuilder_1.EMOJIS.ACTIONS.SENDER} Initiated By`,
            value: `${initiator.displayName} (${isSellerInitiated ? 'Seller' : 'Buyer'})`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} ${isSellerInitiated ? 'Buyer' : 'Seller'}`,
            value: recipient.displayName,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.TIMER} Status`,
            value: `${embedBuilder_1.EMOJIS.TRADE.PENDING} Pending Acceptance`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
            value: (0, embedBuilder_1.formatCoins)(trade.amount),
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
            value: trade.itemDescription,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Expires`,
            value: `<t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>`,
            inline: true
        }
    ]);
    if (trade.notes) {
        embed.addFields([{
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Notes`,
                value: trade.notes,
                inline: false
            }]);
    }
    embed.addFields([{
            name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
            value: `\`${trade.tradeId}\``,
            inline: false
        }]);
    return embed;
}
/**
 * Create trade proposal action buttons
 */
function createTradeProposalButtons(tradeId) {
    return new discord_js_1.ActionRowBuilder().addComponents(new discord_js_1.ButtonBuilder()
        .setCustomId(`trade_accept_${tradeId}`)
        .setLabel('Accept Trade')
        .setEmoji(embedBuilder_1.EMOJIS.SUCCESS.CHECK)
        .setStyle(discord_js_1.ButtonStyle.Success), new discord_js_1.ButtonBuilder()
        .setCustomId(`trade_decline_${tradeId}`)
        .setLabel('Decline Trade')
        .setEmoji('❌')
        .setStyle(discord_js_1.ButtonStyle.Danger), new discord_js_1.ButtonBuilder()
        .setCustomId(`trade_details_${tradeId}`)
        .setLabel('View Details')
        .setEmoji(embedBuilder_1.EMOJIS.MISC.MAGNIFYING)
        .setStyle(discord_js_1.ButtonStyle.Secondary));
}
/**
 * Create active trade embed
 */
function createActiveTradeEmbed(trade, seller, buyer) {
    const embed = (0, embedBuilder_1.createBaseEmbed)(`${embedBuilder_1.EMOJIS.TRADE.ACTIVE} Active Trade`, undefined, embedBuilder_1.COLORS.SUCCESS);
    embed.addFields([
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} Seller`,
            value: `${seller.displayName}${trade.sellerConfirmed ? ` ${embedBuilder_1.EMOJIS.TRADE.CONFIRMATION}` : ''}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} Buyer`,
            value: `${buyer.displayName}${trade.buyerConfirmed ? ` ${embedBuilder_1.EMOJIS.TRADE.CONFIRMATION}` : ''}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.TIMER} Status`,
            value: `${embedBuilder_1.EMOJIS.TRADE.ACTIVE} Active`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
            value: (0, embedBuilder_1.formatCoins)(trade.amount),
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.ESCROW} Escrow`,
            value: trade.escrowLocked ? `${embedBuilder_1.EMOJIS.TRADE.ESCROW} Locked` : `${embedBuilder_1.EMOJIS.TRADE.RELEASE} Not Locked`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Expires`,
            value: `<t:${Math.floor(trade.expiresAt.getTime() / 1000)}:R>`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
            value: trade.itemDescription,
            inline: false
        }
    ]);
    if (trade.notes) {
        embed.addFields([{
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Notes`,
                value: trade.notes,
                inline: false
            }]);
    }
    // Add confirmation status
    const confirmationStatus = [];
    if (trade.sellerConfirmed) {
        confirmationStatus.push(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Seller confirmed`);
    }
    else {
        confirmationStatus.push(`${embedBuilder_1.EMOJIS.TRADE.PENDING} Seller pending`);
    }
    if (trade.buyerConfirmed) {
        confirmationStatus.push(`${embedBuilder_1.EMOJIS.SUCCESS.CHECK} Buyer confirmed`);
    }
    else {
        confirmationStatus.push(`${embedBuilder_1.EMOJIS.TRADE.PENDING} Buyer pending`);
    }
    embed.addFields([{
            name: `${embedBuilder_1.EMOJIS.TRADE.CONFIRMATION} Confirmation Status`,
            value: confirmationStatus.join('\n'),
            inline: false
        }]);
    embed.addFields([{
            name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
            value: `\`${trade.tradeId}\``,
            inline: false
        }]);
    return embed;
}
/**
 * Create active trade action buttons
 */
function createActiveTradeButtons(tradeId, userConfirmed) {
    const buttons = [];
    if (!userConfirmed) {
        buttons.push(new discord_js_1.ButtonBuilder()
            .setCustomId(`trade_confirm_${tradeId}`)
            .setLabel('Confirm Completion')
            .setEmoji(embedBuilder_1.EMOJIS.TRADE.CONFIRMATION)
            .setStyle(discord_js_1.ButtonStyle.Success));
    }
    buttons.push(new discord_js_1.ButtonBuilder()
        .setCustomId(`trade_dispute_${tradeId}`)
        .setLabel('Dispute Trade')
        .setEmoji(embedBuilder_1.EMOJIS.TRADE.DISPUTED)
        .setStyle(discord_js_1.ButtonStyle.Danger), new discord_js_1.ButtonBuilder()
        .setCustomId(`trade_cancel_${tradeId}`)
        .setLabel('Cancel Trade')
        .setEmoji(embedBuilder_1.EMOJIS.TRADE.CANCELLED)
        .setStyle(discord_js_1.ButtonStyle.Secondary));
    return new discord_js_1.ActionRowBuilder().addComponents(...buttons);
}
/**
 * Create completed trade embed
 */
function createCompletedTradeEmbed(trade, seller, buyer) {
    const embed = (0, embedBuilder_1.createBaseEmbed)(`${embedBuilder_1.EMOJIS.TRADE.COMPLETED} Trade Completed`, undefined, embedBuilder_1.COLORS.SUCCESS);
    embed.addFields([
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} Seller`,
            value: `${seller.displayName} ${embedBuilder_1.EMOJIS.TRADE.CONFIRMATION}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} Buyer`,
            value: `${buyer.displayName} ${embedBuilder_1.EMOJIS.TRADE.CONFIRMATION}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.TIMER} Status`,
            value: `${embedBuilder_1.EMOJIS.TRADE.COMPLETED} Completed`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
            value: (0, embedBuilder_1.formatCoins)(trade.amount),
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Completed`,
            value: trade.completedAt ? `<t:${Math.floor(trade.completedAt.getTime() / 1000)}:R>` : 'Unknown',
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.RELEASE} Funds`,
            value: `${embedBuilder_1.EMOJIS.TRADE.RELEASE} Released to Seller`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
            value: trade.itemDescription,
            inline: false
        }
    ]);
    if (trade.notes) {
        embed.addFields([{
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Notes`,
                value: trade.notes,
                inline: false
            }]);
    }
    embed.addFields([{
            name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
            value: `\`${trade.tradeId}\``,
            inline: false
        }]);
    return embed;
}
/**
 * Create cancelled trade embed
 */
function createCancelledTradeEmbed(trade, seller, buyer, reason) {
    const embed = (0, embedBuilder_1.createBaseEmbed)(`${embedBuilder_1.EMOJIS.TRADE.CANCELLED} Trade Cancelled`, undefined, embedBuilder_1.COLORS.ERROR);
    embed.addFields([
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} Seller`,
            value: seller.displayName,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.USER} Buyer`,
            value: buyer.displayName,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.TIMER} Status`,
            value: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} Cancelled`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Amount`,
            value: (0, embedBuilder_1.formatCoins)(trade.amount),
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.RELEASE} Funds`,
            value: trade.escrowLocked ? `${embedBuilder_1.EMOJIS.TRADE.RELEASE} Refunded to Buyer` : 'No Escrow',
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Cancelled`,
            value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.PACKAGE} Item`,
            value: trade.itemDescription,
            inline: false
        }
    ]);
    if (reason) {
        embed.addFields([{
                name: `${embedBuilder_1.EMOJIS.MISC.SCROLL} Cancellation Reason`,
                value: reason,
                inline: false
            }]);
    }
    embed.addFields([{
            name: `${embedBuilder_1.EMOJIS.MISC.ID} Trade ID`,
            value: `\`${trade.tradeId}\``,
            inline: false
        }]);
    return embed;
}
/**
 * Create user trade stats embed
 */
function createUserTradeStatsEmbed(user, stats) {
    const embed = (0, embedBuilder_1.createBaseEmbed)(`${embedBuilder_1.EMOJIS.SUCCESS.TROPHY} Trade Statistics`, `Statistics for ${user.displayName}`, embedBuilder_1.COLORS.INFO);
    // Calculate derived stats
    const successRate = stats.totalTrades > 0 ? (stats.successfulTrades / stats.totalTrades * 100).toFixed(1) : '0';
    const disputeRate = stats.totalTrades > 0 ? (stats.disputedTrades / stats.totalTrades * 100).toFixed(1) : '0';
    embed.addFields([
        {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.CHART} Overview`,
            value: `**Total Trades:** ${stats.totalTrades}\n` +
                `**Successful:** ${stats.successfulTrades} (${successRate}%)\n` +
                `**Active:** ${stats.activeTrades}\n` +
                `**Reputation:** ${stats.reputationScore}/100`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.ECONOMY.COINS} Financial`,
            value: `**Volume Traded:** ${(0, embedBuilder_1.formatCoins)(stats.totalVolumeTraded)}\n` +
                `**Average Trade:** ${(0, embedBuilder_1.formatCoins)(Math.round(stats.averageTradeValue))}\n` +
                `**Largest Trade:** ${(0, embedBuilder_1.formatCoins)(stats.largestTrade)}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.SCALES} Performance`,
            value: `**Completion Rate:** ${(stats.completionRate * 100).toFixed(1)}%\n` +
                `**Dispute Rate:** ${disputeRate}%\n` +
                `**Avg. Completion:** ${stats.averageCompletionTime.toFixed(1)}h`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.ROLES.MEDAL} Role Distribution`,
            value: `**As Seller:** ${stats.tradesAsSeller}\n` +
                `**As Buyer:** ${stats.tradesAsBuyer}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.TRADE.TIMER} Status`,
            value: `**Cancelled:** ${stats.cancelledTrades}\n` +
                `**Expired:** ${stats.expiredTrades}\n` +
                `**Disputed:** ${stats.disputedTrades}`,
            inline: true
        },
        {
            name: `${embedBuilder_1.EMOJIS.MISC.CLOCK} Activity`,
            value: `**Daily Trades:** ${stats.dailyTradeCount}/${constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY}\n` +
                `**Last Trade:** <t:${Math.floor(stats.lastTradeDate.getTime() / 1000)}:R>\n` +
                `**Restricted:** ${stats.isRestricted ? 'Yes' : 'No'}`,
            inline: true
        }
    ]);
    if (stats.isRestricted && stats.restrictedUntil) {
        embed.addFields([{
                name: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Restriction`,
                value: `**Reason:** ${stats.restrictionReason || 'Not specified'}\n` +
                    `**Until:** <t:${Math.floor(stats.restrictedUntil.getTime() / 1000)}:R>`,
                inline: false
            }]);
    }
    return embed;
}
/**
 * Get trade state color
 */
function getTradeStateColor(state) {
    switch (state) {
        case 'PROPOSED':
            return embedBuilder_1.COLORS.WARNING;
        case 'ACCEPTED':
        case 'ACTIVE':
            return embedBuilder_1.COLORS.INFO;
        case 'COMPLETED':
            return embedBuilder_1.COLORS.SUCCESS;
        case 'CANCELLED':
        case 'EXPIRED':
            return embedBuilder_1.COLORS.ERROR;
        case 'DISPUTED':
            return embedBuilder_1.COLORS.WARNING;
        default:
            return embedBuilder_1.COLORS.PRIMARY;
    }
}
/**
 * Get trade state emoji
 */
function getTradeStateEmoji(state) {
    switch (state) {
        case 'PROPOSED':
            return embedBuilder_1.EMOJIS.TRADE.PENDING;
        case 'ACCEPTED':
        case 'ACTIVE':
            return embedBuilder_1.EMOJIS.TRADE.ACTIVE;
        case 'COMPLETED':
            return embedBuilder_1.EMOJIS.TRADE.COMPLETED;
        case 'CANCELLED':
            return embedBuilder_1.EMOJIS.TRADE.CANCELLED;
        case 'EXPIRED':
            return embedBuilder_1.EMOJIS.TRADE.EXPIRED;
        case 'DISPUTED':
            return embedBuilder_1.EMOJIS.TRADE.DISPUTED;
        default:
            return embedBuilder_1.EMOJIS.MISC.CLOCK;
    }
}
//# sourceMappingURL=tradeEmbedBuilder.js.map