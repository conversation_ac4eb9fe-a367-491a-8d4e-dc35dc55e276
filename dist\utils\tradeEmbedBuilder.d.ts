/**
 * Trade Embed Builder
 * Specialized embed builders for trade system UI components
 */
import { Embed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, User as DiscordUser, ColorResolvable } from 'discord.js';
import { ITrade, IUserTradeStats } from '../models';
/**
 * Create a trade proposal embed
 */
export declare function createTradeProposalEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser): EmbedBuilder;
/**
 * Create trade proposal action buttons
 */
export declare function createTradeProposalButtons(tradeId: string): ActionRowBuilder<ButtonBuilder>;
/**
 * Create active trade embed
 */
export declare function createActiveTradeEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser): EmbedBuilder;
/**
 * Create active trade action buttons
 */
export declare function createActiveTradeButtons(tradeId: string, userConfirmed: boolean): ActionRowBuilder<ButtonBuilder>;
/**
 * Create completed trade embed
 */
export declare function createCompletedTradeEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser): EmbedBuilder;
/**
 * <PERSON>reate cancelled trade embed
 */
export declare function createCancelledTradeEmbed(trade: ITrade, seller: DiscordUser, buyer: DiscordUser, reason?: string): EmbedBuilder;
/**
 * Create user trade stats embed
 */
export declare function createUserTradeStatsEmbed(user: DiscordUser, stats: IUserTradeStats): EmbedBuilder;
/**
 * Get trade state color
 */
export declare function getTradeStateColor(state: string): ColorResolvable;
/**
 * Get trade state emoji
 */
export declare function getTradeStateEmoji(state: string): string;
//# sourceMappingURL=tradeEmbedBuilder.d.ts.map