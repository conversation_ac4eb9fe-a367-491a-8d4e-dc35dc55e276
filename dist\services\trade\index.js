"use strict";
/**
 * Trade Service Module Index
 * Centralized exports for the trade system
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TRADE = exports.UserTradeStats = exports.TradeConfirmation = exports.DisputeCase = exports.EscrowTransaction = exports.Trade = exports.TradeNotificationManager = exports.TradeValidator = exports.EscrowManager = exports.TradeService = void 0;
// Main Service
var TradeService_1 = require("./TradeService");
Object.defineProperty(exports, "TradeService", { enumerable: true, get: function () { return TradeService_1.TradeService; } });
// Managers
var EscrowManager_1 = require("./managers/EscrowManager");
Object.defineProperty(exports, "EscrowManager", { enumerable: true, get: function () { return EscrowManager_1.EscrowManager; } });
var TradeValidator_1 = require("./managers/TradeValidator");
Object.defineProperty(exports, "TradeValidator", { enumerable: true, get: function () { return TradeValidator_1.TradeValidator; } });
var TradeNotificationManager_1 = require("./managers/TradeNotificationManager");
Object.defineProperty(exports, "TradeNotificationManager", { enumerable: true, get: function () { return TradeNotificationManager_1.TradeNotificationManager; } });
// Re-export trade-related models and types
var models_1 = require("../../models");
Object.defineProperty(exports, "Trade", { enumerable: true, get: function () { return models_1.Trade; } });
Object.defineProperty(exports, "EscrowTransaction", { enumerable: true, get: function () { return models_1.EscrowTransaction; } });
Object.defineProperty(exports, "DisputeCase", { enumerable: true, get: function () { return models_1.DisputeCase; } });
Object.defineProperty(exports, "TradeConfirmation", { enumerable: true, get: function () { return models_1.TradeConfirmation; } });
Object.defineProperty(exports, "UserTradeStats", { enumerable: true, get: function () { return models_1.UserTradeStats; } });
// Trade system constants
var constants_1 = require("../../config/constants");
Object.defineProperty(exports, "TRADE", { enumerable: true, get: function () { return constants_1.TRADE; } });
//# sourceMappingURL=index.js.map