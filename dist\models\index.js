"use strict";
/**
 * Models Index
 * Centralized exports for all database models
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTradeStats = exports.TradeConfirmation = exports.DisputeCase = exports.EscrowTransaction = exports.Trade = exports.StarterBalance = exports.UserActivity = exports.Transaction = exports.User = void 0;
exports.initializeModels = initializeModels;
// Core Models
var User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return __importDefault(User_1).default; } });
var Transaction_1 = require("./Transaction");
Object.defineProperty(exports, "Transaction", { enumerable: true, get: function () { return __importDefault(Transaction_1).default; } });
var UserActivity_1 = require("./UserActivity");
Object.defineProperty(exports, "UserActivity", { enumerable: true, get: function () { return __importDefault(UserActivity_1).default; } });
var StarterBalance_1 = require("./StarterBalance");
Object.defineProperty(exports, "StarterBalance", { enumerable: true, get: function () { return __importDefault(StarterBalance_1).default; } });
// Trade System Models
var Trade_1 = require("./Trade");
Object.defineProperty(exports, "Trade", { enumerable: true, get: function () { return __importDefault(Trade_1).default; } });
var EscrowTransaction_1 = require("./EscrowTransaction");
Object.defineProperty(exports, "EscrowTransaction", { enumerable: true, get: function () { return __importDefault(EscrowTransaction_1).default; } });
var DisputeCase_1 = require("./DisputeCase");
Object.defineProperty(exports, "DisputeCase", { enumerable: true, get: function () { return __importDefault(DisputeCase_1).default; } });
var TradeConfirmation_1 = require("./TradeConfirmation");
Object.defineProperty(exports, "TradeConfirmation", { enumerable: true, get: function () { return __importDefault(TradeConfirmation_1).default; } });
var UserTradeStats_1 = require("./UserTradeStats");
Object.defineProperty(exports, "UserTradeStats", { enumerable: true, get: function () { return __importDefault(UserTradeStats_1).default; } });
/**
 * Model Initialization
 * Call this function to ensure all models are properly registered
 */
async function initializeModels() {
    // Import all models to ensure they are registered with Mongoose
    await Promise.all([
        Promise.resolve().then(() => __importStar(require('./User'))),
        Promise.resolve().then(() => __importStar(require('./Transaction'))),
        Promise.resolve().then(() => __importStar(require('./UserActivity'))),
        Promise.resolve().then(() => __importStar(require('./StarterBalance'))),
        Promise.resolve().then(() => __importStar(require('./Trade'))),
        Promise.resolve().then(() => __importStar(require('./EscrowTransaction'))),
        Promise.resolve().then(() => __importStar(require('./DisputeCase'))),
        Promise.resolve().then(() => __importStar(require('./TradeConfirmation'))),
        Promise.resolve().then(() => __importStar(require('./UserTradeStats'))),
    ]);
}
//# sourceMappingURL=index.js.map