/**
 * Application Constants
 * Centralized configuration for all constant values used throughout the application
 */
export declare const ECONOMY: {
    readonly CURRENCY_NAME: "Phalanx Loyalty Coins";
    readonly CURRENCY_SYMBOL: "PLC";
    readonly CURRENCY_EMOJI: "🪙";
    readonly REACTION_REWARD_AMOUNT: 5;
    readonly REACTION_RATE_LIMIT_SECONDS: 30;
    readonly REACTION_MESSAGE_AGE_LIMIT_HOURS: 24;
    readonly TAX_COLLECTION_INTERVAL_HOURS: 1;
    readonly DEFAULT_STARTER_BALANCE: 0;
};
export declare const MILESTONES: {
    readonly MAX_WEEKLY_MILESTONE_REWARDS: 50;
    readonly MAX_DAILY_MILESTONE_REWARDS: 10;
    readonly SUSPICIOUS_ACTIVITY_THRESHOLD: 5;
    readonly BLACKLIST_DURATION_HOURS: 24;
    readonly DEFAULT_LOGIN_STREAK_REWARD: 10;
    readonly DEFAULT_MESSAGE_COUNT_REWARD: 15;
    readonly DEFAULT_VOICE_TIME_REWARD: 20;
    readonly DEFAULT_DIMINISHING_FACTOR: 0.9;
};
export declare const DYNASTY: {
    readonly MIN_PLC_REQUIREMENT: 5000;
    readonly MIN_TENURE_DAYS: 30;
    readonly MILESTONE_BONUS_PERCENTAGE: 10;
    readonly MAX_LEVEL: 10;
    readonly BASE_LEVEL_REQUIREMENT: 1000;
    readonly LEVEL_MULTIPLIER: 1.5;
};
export declare const TRADE: {
    readonly MIN_TRADE_AMOUNT: 100;
    readonly MAX_TRADE_AMOUNT: 100000;
    readonly MAX_ACTIVE_TRADES_PER_USER: 5;
    readonly MAX_TRADE_PROPOSALS_PER_DAY: 10;
    readonly TRADE_EXPIRATION_HOURS: 48;
    readonly WARNING_HOURS: readonly [6, 1];
    readonly PARTIAL_CONFIRMATION_EXTENSION_HOURS: 12;
    readonly APPEAL_WINDOW_HOURS: 24;
    readonly TRADE_COOLDOWN_SECONDS: 30;
    readonly DISPUTE_COOLDOWN_HOURS: 1;
    readonly DISPUTE_RATIO_THRESHOLD: 0.2;
    readonly TRADE_RESTRICTION_DAYS: 7;
    readonly MIN_TRADES_FOR_REPUTATION: 5;
    readonly STATES: {
        readonly PROPOSED: "PROPOSED";
        readonly ACCEPTED: "ACCEPTED";
        readonly ACTIVE: "ACTIVE";
        readonly COMPLETED: "COMPLETED";
        readonly CANCELLED: "CANCELLED";
        readonly EXPIRED: "EXPIRED";
        readonly DISPUTED: "DISPUTED";
    };
    readonly MAX_EVIDENCE_FILES: 5;
    readonly EVIDENCE_FILE_SIZE_MB: 10;
};
export declare const DISCORD: {
    readonly REQUIRED_INTENTS: readonly ["Guilds", "GuildMessages", "MessageContent", "GuildMembers", "GuildMessageReactions"];
    readonly ADMIN_PERMISSIONS: readonly ["Administrator", "ManageGuild", "ManageRoles"];
    readonly COMMAND_COOLDOWN_SECONDS: 3;
    readonly ADMIN_COMMAND_COOLDOWN_SECONDS: 2;
    readonly MAX_EMBED_FIELDS: 25;
    readonly MAX_EMBED_DESCRIPTION_LENGTH: 4096;
    readonly MAX_BUTTON_LABEL_LENGTH: 80;
};
export declare const DATABASE: {
    readonly CONNECTION_TIMEOUT_MS: 30000;
    readonly OPERATION_TIMEOUT_MS: 10000;
    readonly USER_CLEANUP_GRACE_PERIOD_MS: 5000;
    readonly REQUIRED_INDEXES: readonly [{
        readonly collection: "users";
        readonly index: {
            readonly discordId: 1;
        };
        readonly unique: true;
    }, {
        readonly collection: "transactions";
        readonly index: {
            readonly discordId: 1;
            readonly timestamp: -1;
        };
    }, {
        readonly collection: "reactionrewards";
        readonly index: {
            readonly userId: 1;
            readonly timestamp: -1;
        };
    }, {
        readonly collection: "milestoneconfigurations";
        readonly index: {
            readonly guildId: 1;
            readonly milestoneType: 1;
        };
        readonly unique: true;
    }];
};
export declare const ERROR_HANDLING: {
    readonly CATEGORIES: {
        readonly DATABASE: "Database Error";
        readonly VALIDATION: "Validation Error";
        readonly PERMISSION: "Permission Error";
        readonly RATE_LIMIT: "Rate Limit Error";
        readonly NETWORK: "Network Error";
        readonly UNKNOWN: "Unknown Error";
    };
    readonly MAX_RETRIES: 3;
    readonly RETRY_DELAY_MS: 1000;
    readonly EXPONENTIAL_BACKOFF: true;
};
export declare const LOGGING: {
    readonly LEVELS: {
        readonly ERROR: "error";
        readonly WARN: "warn";
        readonly INFO: "info";
        readonly DEBUG: "debug";
    };
    readonly CATEGORIES: {
        readonly ECONOMY: "economy";
        readonly MILESTONE: "milestone";
        readonly DYNASTY: "dynasty";
        readonly ADMIN: "admin";
        readonly DATABASE: "database";
        readonly DISCORD: "discord";
    };
};
export declare const FEATURES: {
    readonly ECONOMY_SYSTEM: true;
    readonly MILESTONE_SYSTEM: true;
    readonly DYNASTY_SYSTEM: true;
    readonly REACTION_REWARDS: true;
    readonly TRADE_SYSTEM: true;
    readonly TAX_SYSTEM: true;
    readonly STARTER_BALANCE: true;
    readonly AUTO_MESSAGES: true;
    readonly ROLE_AUTOMATION: true;
    readonly USER_CLEANUP: true;
    readonly AUDIT_LOGGING: true;
    readonly DEBUG_MODE: false;
    readonly VERBOSE_LOGGING: false;
};
export declare const VALIDATION: {
    readonly DISCORD_ID_REGEX: RegExp;
    readonly MIN_TRANSACTION_AMOUNT: 1;
    readonly MAX_TRANSACTION_AMOUNT: 1000000;
    readonly MAX_DESCRIPTION_LENGTH: 500;
    readonly MAX_REASON_LENGTH: 200;
    readonly MAX_NAME_LENGTH: 100;
    readonly MAX_ROLES_PER_GUILD: 100;
    readonly MAX_USERS_PER_OPERATION: 100;
};
export declare const SCHEDULES: {
    readonly TAX_COLLECTION: "0 * * * *";
    readonly MILESTONE_RESET: "0 0 * * *";
    readonly USER_CLEANUP: "0 2 * * *";
    readonly AUDIT_CLEANUP: "0 3 * * 0";
};
//# sourceMappingURL=constants.d.ts.map