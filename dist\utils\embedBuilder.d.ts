import { Em<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>er, But<PERSON><PERSON>uilder, User, ColorResolvable } from 'discord.js';
export declare const COLORS: {
    PRIMARY: ColorResolvable;
    SUCCESS: ColorResolvable;
    ERROR: ColorResolvable;
    WARNING: ColorResolvable;
    INFO: ColorResolvable;
    GOLD: ColorResolvable;
};
export declare const EMOJIS: {
    ECONOMY: {
        COINS: string;
        MONEY: string;
        DIAMOND: string;
        DOLLAR: string;
        SPARKLES: string;
        BANK: string;
        CHART: string;
        TRANSFER: string;
    };
    SUCCESS: {
        CHECK: string;
        PARTY: string;
        STAR: string;
        CROWN: string;
        TROPHY: string;
        THUMBS_UP: string;
        REFRESH: string;
    };
    ROLES: {
        MEDAL: string;
        MASK: string;
        SHIELD: string;
        ARMOR: string;
        BADGE: string;
        RIBBON: string;
    };
    ACTIONS: {
        LIGHTNING: string;
        ROCKET: string;
        TARGET: string;
        FIRE: string;
        MAGIC: string;
        GEAR: string;
        SENDER: string;
    };
    ADMIN: {
        SCALES: string;
        HAMMER: string;
        WARNING: string;
        TOOLS: string;
        LOCK: string;
        KEY: string;
        INFO: string;
        LIST: string;
        CLOCK: string;
        SETTINGS: string;
    };
    MILESTONE: {
        TROPHY: string;
        STAR: string;
        MEDAL: string;
        STREAK: string;
        DIVERSITY: string;
        VOICE: string;
        MESSAGE: string;
        ANNIVERSARY: string;
        PROGRESS: string;
        ACHIEVEMENT: string;
    };
    TRADE: {
        HANDSHAKE: string;
        PACKAGE: string;
        SCALES: string;
        HOURGLASS: string;
        TIMER: string;
        PENDING: string;
        ACTIVE: string;
        COMPLETED: string;
        CANCELLED: string;
        EXPIRED: string;
        DISPUTED: string;
        ESCROW: string;
        RELEASE: string;
        PROPOSAL: string;
        CONFIRMATION: string;
    };
    MISC: {
        CLOCK: string;
        CALENDAR: string;
        BOOK: string;
        SCROLL: string;
        MAGNIFYING: string;
        BELL: string;
        LIGHTBULB: string;
        ID: string;
        USER: string;
        GUILD: string;
        ROLE: string;
        SPARKLES: string;
        LINK: string;
        TAG: string;
        ATTACHMENT: string;
    };
};
/**
 * Creates a base embed with consistent branding
 */
export declare function createBaseEmbed(title?: string, description?: string, color?: ColorResolvable): EmbedBuilder;
/**
 * Creates a success embed
 */
export declare function createSuccessEmbed(title: string, description?: string): EmbedBuilder;
/**
 * Creates an error embed
 */
export declare function createErrorEmbed(title: string, description?: string): EmbedBuilder;
/**
 * Creates an economy-themed embed
 */
export declare function createEconomyEmbed(title: string, description?: string): EmbedBuilder;
/**
 * Creates an admin-themed embed
 */
export declare function createAdminEmbed(title: string, description?: string): EmbedBuilder;
/**
 * Adds user information to an embed
 */
export declare function addUserInfo(embed: EmbedBuilder, user: User): EmbedBuilder;
/**
 * Creates quick action buttons
 */
export declare function createQuickActionButtons(): ActionRowBuilder<ButtonBuilder>;
/**
 * Creates navigation buttons for paginated content
 */
export declare function createNavigationButtons(currentPage: number, totalPages: number, disabled?: boolean): ActionRowBuilder<ButtonBuilder>;
/**
 * Creates confirmation buttons
 */
export declare function createConfirmationButtons(confirmId: string, cancelId: string): ActionRowBuilder<ButtonBuilder>;
/**
 * Formats a number with appropriate emoji and styling
 */
export declare function formatCoins(amount: number): string;
/**
 * Creates a loading embed
 */
export declare function createLoadingEmbed(message?: string): EmbedBuilder;
//# sourceMappingURL=embedBuilder.d.ts.map