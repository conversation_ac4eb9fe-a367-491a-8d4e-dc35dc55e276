import { Document } from 'mongoose';
export interface IDisputeCase extends Document {
    disputeId: string;
    tradeId: string;
    guildId: string;
    initiatorId: string;
    respondentId: string;
    assignedAdminId?: string;
    reason: string;
    description?: string;
    category: 'ITEM_NOT_RECEIVED' | 'ITEM_NOT_AS_DESCRIBED' | 'PAYMENT_ISSUE' | 'COMMUNICATION_ISSUE' | 'OTHER';
    initiatorEvidence: string[];
    respondentEvidence: string[];
    evidenceDeadline: Date;
    status: 'OPEN' | 'EVIDENCE_COLLECTION' | 'UNDER_REVIEW' | 'RESOLVED' | 'APPEALED' | 'CLOSED';
    priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    resolution?: 'FAVOR_INITIATOR' | 'FAVOR_RESPONDENT' | 'SPLIT_ESCROW' | 'FULL_REFUND' | 'CUSTOM';
    resolutionDetails?: string;
    resolutionAmount?: number;
    resolvedAt?: Date;
    appealedBy?: string;
    appealReason?: string;
    appealedAt?: Date;
    appealDeadline?: Date;
    createdAt: Date;
    lastUpdatedAt: Date;
    adminNotes: string[];
    internalComments?: string;
    escalationLevel: number;
    tags: string[];
}
declare const _default: import("mongoose").Model<IDisputeCase, {}, {}, {}, Document<unknown, {}, IDisputeCase, {}> & IDisputeCase & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=DisputeCase.d.ts.map