"use strict";
/**
 * Trade Security Service
 * Handles security, anti-abuse, and rate limiting for the trade system
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradeSecurityService = void 0;
const BaseService_1 = require("../base/BaseService");
const decorators_1 = require("../../core/decorators");
const constants_1 = require("../../config/constants");
const models_1 = require("../../models");
/**
 * Trade Security Service Class
 */
class TradeSecurityService extends BaseService_1.BaseService {
    constructor(app) {
        super('TradeSecurityService', app);
        this.rateLimitCache = new Map();
        this.suspiciousActivityCache = new Map();
    }
    /**
     * Initialize the security service
     */
    async onInitialize() {
        this.logger.info('[TradeSecurityService] Trade security service initialized');
        // Start cleanup interval for rate limit cache
        setInterval(() => {
            this.cleanupRateLimitCache();
        }, 5 * 60 * 1000); // Every 5 minutes
    }
    /**
     * Check if user can initiate a trade (rate limiting)
     */
    async checkTradeRateLimit(discordId, guildId) {
        this.logOperation('Checking trade rate limit', { discordId, guildId });
        try {
            // Check daily trade limit from user stats
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
            if (userStats) {
                userStats.resetDailyCountIfNeeded();
                if (userStats.dailyTradeCount >= constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY) {
                    return {
                        allowed: false,
                        reason: `Daily trade limit of ${constants_1.TRADE.MAX_TRADE_PROPOSALS_PER_DAY} reached`,
                        resetTime: this.getNextMidnight()
                    };
                }
                if (userStats.activeTrades >= constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER) {
                    return {
                        allowed: false,
                        reason: `Maximum of ${constants_1.TRADE.MAX_ACTIVE_TRADES_PER_USER} active trades allowed`
                    };
                }
            }
            // Check short-term rate limiting (cooldown)
            const rateLimitKey = `${discordId}:${guildId}`;
            const rateLimitData = this.rateLimitCache.get(rateLimitKey);
            if (rateLimitData) {
                const now = new Date();
                const timeSinceLastRequest = now.getTime() - rateLimitData.lastRequest.getTime();
                if (timeSinceLastRequest < constants_1.TRADE.TRADE_COOLDOWN_SECONDS * 1000) {
                    const remainingSeconds = Math.ceil((constants_1.TRADE.TRADE_COOLDOWN_SECONDS * 1000 - timeSinceLastRequest) / 1000);
                    return {
                        allowed: false,
                        reason: `Please wait ${remainingSeconds} seconds before creating another trade`,
                        resetTime: new Date(rateLimitData.lastRequest.getTime() + constants_1.TRADE.TRADE_COOLDOWN_SECONDS * 1000)
                    };
                }
            }
            // Update rate limit cache
            this.rateLimitCache.set(rateLimitKey, {
                count: (rateLimitData?.count || 0) + 1,
                resetTime: this.getNextMidnight(),
                lastRequest: new Date()
            });
            return { allowed: true };
        }
        catch (error) {
            this.handleError(error, { operation: 'check_trade_rate_limit', discordId, guildId });
            throw error;
        }
    }
    /**
     * Perform comprehensive security check on a trade
     */
    async performSecurityCheck(trade) {
        this.logOperation('Performing security check', { tradeId: trade.tradeId });
        try {
            const violations = [];
            const recommendations = [];
            let riskScore = 0;
            // Check user risk profiles
            const sellerRisk = await this.getUserRiskProfile(trade.sellerId, trade.guildId);
            const buyerRisk = await this.getUserRiskProfile(trade.buyerId, trade.guildId);
            if (sellerRisk.riskLevel === 'HIGH' || sellerRisk.riskLevel === 'CRITICAL') {
                violations.push(`Seller has ${sellerRisk.riskLevel} risk profile`);
                riskScore += sellerRisk.riskLevel === 'CRITICAL' ? 40 : 25;
            }
            if (buyerRisk.riskLevel === 'HIGH' || buyerRisk.riskLevel === 'CRITICAL') {
                violations.push(`Buyer has ${buyerRisk.riskLevel} risk profile`);
                riskScore += buyerRisk.riskLevel === 'CRITICAL' ? 40 : 25;
            }
            // Check for suspicious patterns
            const suspiciousPatterns = await this.detectSuspiciousPatterns(trade);
            violations.push(...suspiciousPatterns.violations);
            riskScore += suspiciousPatterns.riskScore;
            // Check trade amount patterns
            const amountCheck = this.checkTradeAmount(trade);
            violations.push(...amountCheck.violations);
            riskScore += amountCheck.riskScore;
            // Check for rapid trading patterns
            const rapidTradingCheck = await this.checkRapidTrading(trade);
            violations.push(...rapidTradingCheck.violations);
            riskScore += rapidTradingCheck.riskScore;
            // Generate recommendations
            if (riskScore > 30) {
                recommendations.push('Consider manual review before approval');
            }
            if (riskScore > 50) {
                recommendations.push('Require additional verification');
            }
            if (riskScore > 70) {
                recommendations.push('Flag for immediate admin attention');
            }
            // Determine overall risk level
            let riskLevel;
            if (riskScore >= 80)
                riskLevel = 'CRITICAL';
            else if (riskScore >= 60)
                riskLevel = 'HIGH';
            else if (riskScore >= 30)
                riskLevel = 'MEDIUM';
            else
                riskLevel = 'LOW';
            const passed = riskLevel !== 'CRITICAL' && violations.length === 0;
            this.logOperation('Security check completed', {
                tradeId: trade.tradeId,
                riskLevel,
                riskScore,
                violationCount: violations.length,
                passed
            });
            return {
                passed,
                violations,
                riskLevel,
                recommendations
            };
        }
        catch (error) {
            this.handleError(error, { operation: 'perform_security_check', tradeId: trade.tradeId });
            throw error;
        }
    }
    /**
     * Get user risk profile
     */
    async getUserRiskProfile(discordId, guildId) {
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
            if (!userStats) {
                return {
                    discordId,
                    riskLevel: 'LOW',
                    factors: ['New user'],
                    score: 10,
                    lastUpdated: new Date()
                };
            }
            const factors = [];
            let score = 0;
            // High dispute ratio
            if (userStats.disputeRatio > constants_1.TRADE.DISPUTE_RATIO_THRESHOLD) {
                factors.push(`High dispute ratio: ${(userStats.disputeRatio * 100).toFixed(1)}%`);
                score += 30;
            }
            // Low completion rate
            if (userStats.completionRate < 0.8 && userStats.totalTrades >= 5) {
                factors.push(`Low completion rate: ${(userStats.completionRate * 100).toFixed(1)}%`);
                score += 20;
            }
            // Many warnings
            if (userStats.warningsReceived > 2) {
                factors.push(`Multiple warnings: ${userStats.warningsReceived}`);
                score += userStats.warningsReceived * 10;
            }
            // Currently restricted
            if (userStats.isRestricted) {
                factors.push('Currently restricted from trading');
                score += 50;
            }
            // Recent violations
            const recentViolations = userStats.violationHistory.filter(v => {
                const violationDate = new Date(v.split(']')[0].substring(1));
                const daysSince = (Date.now() - violationDate.getTime()) / (1000 * 60 * 60 * 24);
                return daysSince <= 30;
            });
            if (recentViolations.length > 0) {
                factors.push(`Recent violations: ${recentViolations.length}`);
                score += recentViolations.length * 15;
            }
            // Low reputation
            if (userStats.reputationScore < 30) {
                factors.push(`Low reputation: ${userStats.reputationScore}/100`);
                score += 25;
            }
            // Determine risk level
            let riskLevel;
            if (score >= 80)
                riskLevel = 'CRITICAL';
            else if (score >= 60)
                riskLevel = 'HIGH';
            else if (score >= 30)
                riskLevel = 'MEDIUM';
            else
                riskLevel = 'LOW';
            return {
                discordId,
                riskLevel,
                factors,
                score,
                lastUpdated: new Date()
            };
        }
        catch (error) {
            this.handleError(error, { operation: 'get_user_risk_profile', discordId });
            return {
                discordId,
                riskLevel: 'MEDIUM',
                factors: ['Error calculating risk'],
                score: 50,
                lastUpdated: new Date()
            };
        }
    }
    /**
     * Apply automatic restrictions based on user behavior
     */
    async applyAutomaticRestrictions(discordId, guildId) {
        this.logOperation('Checking for automatic restrictions', { discordId, guildId });
        try {
            const userStats = await models_1.UserTradeStats.findOne({ discordId, guildId });
            if (!userStats || userStats.isRestricted) {
                return false; // Already restricted or no stats
            }
            let shouldRestrict = false;
            let restrictionReason = '';
            // Check dispute ratio threshold
            if (userStats.totalTrades >= constants_1.TRADE.MIN_TRADES_FOR_REPUTATION &&
                userStats.disputeRatio > constants_1.TRADE.DISPUTE_RATIO_THRESHOLD) {
                shouldRestrict = true;
                restrictionReason = `High dispute ratio: ${(userStats.disputeRatio * 100).toFixed(1)}%`;
            }
            // Check for multiple recent violations
            const recentViolations = userStats.violationHistory.filter(v => {
                const violationDate = new Date(v.split(']')[0].substring(1));
                const daysSince = (Date.now() - violationDate.getTime()) / (1000 * 60 * 60 * 24);
                return daysSince <= 7;
            });
            if (recentViolations.length >= 3) {
                shouldRestrict = true;
                restrictionReason = `Multiple recent violations: ${recentViolations.length} in 7 days`;
            }
            if (shouldRestrict) {
                // Apply restriction
                const restrictedUntil = new Date();
                restrictedUntil.setDate(restrictedUntil.getDate() + constants_1.TRADE.TRADE_RESTRICTION_DAYS);
                userStats.isRestricted = true;
                userStats.restrictionReason = `Automatic restriction: ${restrictionReason}`;
                userStats.restrictedUntil = restrictedUntil;
                userStats.violationHistory.push(`[${new Date().toISOString()}] Automatic restriction applied: ${restrictionReason}`);
                await userStats.save();
                this.logOperation('Automatic restriction applied', {
                    discordId,
                    guildId,
                    reason: restrictionReason,
                    until: restrictedUntil
                });
                return true;
            }
            return false;
        }
        catch (error) {
            this.handleError(error, { operation: 'apply_automatic_restrictions', discordId, guildId });
            return false;
        }
    }
    // Private helper methods
    async detectSuspiciousPatterns(trade) {
        const violations = [];
        let riskScore = 0;
        try {
            // Check for duplicate trades between same parties
            const recentTrades = await models_1.Trade.find({
                $or: [
                    { sellerId: trade.sellerId, buyerId: trade.buyerId },
                    { sellerId: trade.buyerId, buyerId: trade.sellerId }
                ],
                guildId: trade.guildId,
                createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }, // Last 24 hours
                tradeId: { $ne: trade.tradeId }
            });
            if (recentTrades.length > 2) {
                violations.push(`Multiple trades between same parties: ${recentTrades.length} in 24h`);
                riskScore += 20;
            }
            // Check for round number amounts (potential money laundering)
            if (trade.amount % 1000 === 0 && trade.amount >= 5000) {
                violations.push('Suspiciously round trade amount');
                riskScore += 10;
            }
            // Check for very similar item descriptions
            const similarTrades = await models_1.Trade.find({
                guildId: trade.guildId,
                itemDescription: { $regex: trade.itemDescription.substring(0, 20), $options: 'i' },
                createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }, // Last 7 days
                tradeId: { $ne: trade.tradeId }
            });
            if (similarTrades.length > 3) {
                violations.push('Multiple trades with similar item descriptions');
                riskScore += 15;
            }
        }
        catch (error) {
            this.logger.warn('Error detecting suspicious patterns', { error, tradeId: trade.tradeId });
        }
        return { violations, riskScore };
    }
    checkTradeAmount(trade) {
        const violations = [];
        let riskScore = 0;
        // Very high value trades
        if (trade.amount > 50000) {
            violations.push('Extremely high trade value');
            riskScore += 25;
        }
        else if (trade.amount > 20000) {
            violations.push('High trade value');
            riskScore += 10;
        }
        // Suspiciously low amounts for certain items
        if (trade.itemDescription.toLowerCase().includes('diamond') && trade.amount < 100) {
            violations.push('Suspiciously low amount for valuable item');
            riskScore += 15;
        }
        return { violations, riskScore };
    }
    async checkRapidTrading(trade) {
        const violations = [];
        let riskScore = 0;
        try {
            // Check for rapid trading by seller
            const sellerRecentTrades = await models_1.Trade.find({
                sellerId: trade.sellerId,
                guildId: trade.guildId,
                createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
                tradeId: { $ne: trade.tradeId }
            });
            if (sellerRecentTrades.length > 5) {
                violations.push(`Seller created ${sellerRecentTrades.length} trades in the last hour`);
                riskScore += 20;
            }
            // Check for rapid trading by buyer
            const buyerRecentTrades = await models_1.Trade.find({
                buyerId: trade.buyerId,
                guildId: trade.guildId,
                createdAt: { $gte: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
                tradeId: { $ne: trade.tradeId }
            });
            if (buyerRecentTrades.length > 5) {
                violations.push(`Buyer created ${buyerRecentTrades.length} trades in the last hour`);
                riskScore += 20;
            }
        }
        catch (error) {
            this.logger.warn('Error checking rapid trading', { error, tradeId: trade.tradeId });
        }
        return { violations, riskScore };
    }
    cleanupRateLimitCache() {
        const now = new Date();
        for (const [key, data] of this.rateLimitCache.entries()) {
            if (now > data.resetTime) {
                this.rateLimitCache.delete(key);
            }
        }
    }
    getNextMidnight() {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        tomorrow.setHours(0, 0, 0, 0);
        return tomorrow;
    }
}
exports.TradeSecurityService = TradeSecurityService;
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TradeSecurityService.prototype, "onInitialize", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], TradeSecurityService.prototype, "checkTradeRateLimit", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], TradeSecurityService.prototype, "performSecurityCheck", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], TradeSecurityService.prototype, "applyAutomaticRestrictions", null);
//# sourceMappingURL=TradeSecurityService.js.map