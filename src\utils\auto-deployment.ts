/**
 * Auto-Deployment Utilities
 * 
 * This module provides functions for automating build and deployment processes
 * that would normally be run manually via npm scripts.
 * 
 * These utilities allow the bot to self-build and self-deploy when running on Discloud
 * without requiring manual intervention.
 */

import { REST, Routes } from 'discord.js';
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { getLogger } from '../core/logger';

const execAsync = promisify(exec);
const logger = getLogger();

/**
 * Check if we're running in a production environment (like Discloud)
 */
export function isProductionEnvironment(): boolean {
  return process.env.NODE_ENV === 'production' || 
         process.env.DISCLOUD_ENVIRONMENT === 'true' ||
         process.env.DISCLOUD === 'true';
}

/**
 * Check if TypeScript source files exist but compiled JavaScript files don't
 * This indicates we need to run the build process
 */
export async function needsBuild(): Promise<boolean> {
  try {
    // Check if we have TypeScript source files
    const srcExists = fs.existsSync(path.join(process.cwd(), 'src'));
    if (!srcExists) {
      logger.warn('[AutoDeploy] src directory not found, assuming pre-built deployment');
      return false;
    }

    // Check if dist directory exists and has compiled files
    const distExists = fs.existsSync(path.join(process.cwd(), 'dist'));
    if (!distExists) {
      logger.info('[AutoDeploy] dist directory not found, build needed');
      return true;
    }

    // Check if main.js exists in dist
    const mainJsExists = fs.existsSync(path.join(process.cwd(), 'dist', 'main.js'));
    if (!mainJsExists) {
      logger.info('[AutoDeploy] dist/main.js not found, build needed');
      return true;
    }

    // Check if source files are newer than compiled files
    const srcStat = fs.statSync(path.join(process.cwd(), 'src', 'main.ts'));
    const distStat = fs.statSync(path.join(process.cwd(), 'dist', 'main.js'));
    
    if (srcStat.mtime > distStat.mtime) {
      logger.info('[AutoDeploy] Source files are newer than compiled files, build needed');
      return true;
    }

    logger.info('[AutoDeploy] Compiled files are up to date, no build needed');
    return false;
  } catch (error) {
    logger.error('[AutoDeploy] Error checking build status', { error });
    // If we can't determine, assume we need to build to be safe
    return true;
  }
}

/**
 * Run the TypeScript compiler to build the project
 */
export async function runBuild(): Promise<boolean> {
  logger.info('[AutoDeploy] Starting TypeScript build process...');
  
  try {
    // Check if TypeScript is installed
    const tscPath = path.join(process.cwd(), 'node_modules', '.bin', 'tsc');
    const tscExists = fs.existsSync(tscPath);
    
    if (!tscExists) {
      logger.error('[AutoDeploy] TypeScript compiler not found in node_modules');
      return false;
    }
    
    // Run TypeScript compiler
    const { stdout, stderr } = await execAsync(`"${tscPath}"`);
    
    if (stderr && stderr.trim() !== '') {
      logger.warn('[AutoDeploy] TypeScript compiler warnings/errors:', { stderr });
    }
    
    if (stdout && stdout.trim() !== '') {
      logger.debug('[AutoDeploy] TypeScript compiler output:', { stdout });
    }
    
    // Verify build succeeded by checking for main.js
    const mainJsExists = fs.existsSync(path.join(process.cwd(), 'dist', 'main.js'));
    if (!mainJsExists) {
      logger.error('[AutoDeploy] Build failed - dist/main.js not found after compilation');
      return false;
    }
    
    logger.info('[AutoDeploy] TypeScript build completed successfully');
    return true;
  } catch (error) {
    logger.error('[AutoDeploy] Build process failed', { error });
    return false;
  }
}

/**
 * Deploy slash commands to Discord API
 * This is a programmatic version of the deploy-commands.ts script
 */
export async function deployCommands(): Promise<boolean> {
  logger.info('[AutoDeploy] Starting slash command deployment...');
  
  try {
    // Ensure we have the required environment variables
    if (!process.env.BOT_TOKEN || !process.env.CLIENT_ID) {
      logger.error('[AutoDeploy] Missing required environment variables: BOT_TOKEN and/or CLIENT_ID');
      return false;
    }
    
    // Load commands from the compiled JavaScript files
    const commands = [];
    const commandsPath = path.join(process.cwd(), 'dist', 'commands');
    
    if (!fs.existsSync(commandsPath)) {
      logger.error('[AutoDeploy] Commands directory not found:', commandsPath);
      return false;
    }
    
    // Load legacy commands (individual files)
    const commandFiles = fs.readdirSync(commandsPath).filter(file =>
      file.endsWith('.js') &&
      !file.includes('index') &&
      !file.includes('Manager') &&
      !file.includes('Base')
    );
    
    // Skip files that are handled by new architecture
    const skipFiles = new Set([
      'enhancerole.js',
      'updatenames.js'
    ]);
    
    for (const file of commandFiles) {
      if (skipFiles.has(file)) {
        logger.debug(`[AutoDeploy] Skipping ${file} (handled by new architecture)`);
        continue;
      }
      
      try {
        const command = require(path.join(commandsPath, file));
        if (command.data) {
          commands.push(command.data.toJSON());
          logger.debug(`[AutoDeploy] Loaded legacy command: ${command.data.name}`);
        }
      } catch (error) {
        logger.warn(`[AutoDeploy] Failed to load ${file}:`, error);
      }
    }
    
    // Load new architecture commands
    try {
      const { commandManager } = require(path.join(process.cwd(), 'dist', 'commands', 'CommandManager'));
      await commandManager.loadCommands();
      
      const existingCommandNames = new Set(commands.map(cmd => cmd.name));
      
      const newCommands = commandManager.getDiscordCommands();
      for (const [name, command] of newCommands) {
        if (command.data) {
          // Check for duplicates
          if (existingCommandNames.has(name)) {
            logger.debug(`[AutoDeploy] Skipping new architecture command '${name}' - legacy version already loaded`);
            continue;
          }
          
          commands.push(command.data.toJSON());
          logger.debug(`[AutoDeploy] Loaded new command: ${name}`);
        }
      }
    } catch (error) {
      logger.warn('[AutoDeploy] Failed to load new architecture commands:', error);
    }
    
    logger.info(`[AutoDeploy] Total commands to deploy: ${commands.length}`);
    
    if (commands.length === 0) {
      logger.error('[AutoDeploy] No commands found to deploy!');
      return false;
    }
    
    // Deploy to Discord
    const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);
    
    logger.info('[AutoDeploy] Started refreshing application (/) commands...');
    
    const data = await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: commands }
    ) as any[];
    
    logger.info(`[AutoDeploy] Successfully reloaded ${data.length} application (/) commands.`);
    return true;
  } catch (error) {
    logger.error('[AutoDeploy] Error deploying commands:', error);
    return false;
  }
}

/**
 * Deploy role commands to Discord API
 * This is a programmatic version of the deploy-role-commands.ts script
 */
export async function deployRoleCommands(): Promise<boolean> {
  logger.info('[AutoDeploy] Starting role command deployment...');

  try {
    // Ensure we have the required environment variables
    if (!process.env.BOT_TOKEN || !process.env.CLIENT_ID) {
      logger.error('[AutoDeploy] Missing required environment variables: BOT_TOKEN and/or CLIENT_ID');
      return false;
    }

    // Instead of requiring the external script, implement the role commands deployment directly
    // This provides better error handling and control
    const { REST, Routes, SlashCommandBuilder, PermissionFlagsBits } = require('discord.js');

    // Create the commands manually to avoid dependency issues
    const enhanceRoleCommand = new SlashCommandBuilder()
      .setName('enhancerole')
      .setDescription('Assign a prefix to all users with a specified role')
      .addRoleOption((option: any) =>
        option
          .setName('role')
          .setDescription('Discord role to target')
          .setRequired(true)
      )
      .addStringOption((option: any) =>
        option
          .setName('prefix')
          .setDescription('Text/emoji prefix to prepend (max 10 characters)')
          .setRequired(true)
          .setMaxLength(10)
      )
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames);

    const updateNamesCommand = new SlashCommandBuilder()
      .setName('updatenames')
      .setDescription('Ensure all members have correct prefixes based on their current roles')
      .setDefaultMemberPermissions(PermissionFlagsBits.ManageNicknames);

    const commands = [
      enhanceRoleCommand.toJSON(),
      updateNamesCommand.toJSON()
    ];

    logger.info(`[AutoDeploy] Role commands to deploy: ${commands.length}`);

    // Deploy to Discord
    const rest = new REST({ version: '10' }).setToken(process.env.BOT_TOKEN);

    // Get existing commands first
    const existingCommands = await rest.get(
      Routes.applicationCommands(process.env.CLIENT_ID)
    ) as any[];

    logger.debug(`[AutoDeploy] Found ${existingCommands.length} existing commands`);

    // Add our new commands to existing ones
    const allCommands = [...existingCommands];

    // Remove any existing versions of our commands
    const commandsToRemove = ['enhancerole', 'updatenames'];
    const filteredCommands = allCommands.filter(cmd => !commandsToRemove.includes(cmd.name));

    // Add our new commands
    const finalCommands = [...filteredCommands, ...commands];

    const data = await rest.put(
      Routes.applicationCommands(process.env.CLIENT_ID),
      { body: finalCommands }
    ) as any[];

    logger.info(`[AutoDeploy] Successfully deployed ${data.length} total commands.`);

    // Show our new commands
    const newCommands = data.filter(cmd => commandsToRemove.includes(cmd.name));
    logger.info(`[AutoDeploy] New role prefix commands deployed: ${newCommands.length}`);

    return true;
  } catch (error: any) {
    logger.error('[AutoDeploy] Error deploying role commands:', error);

    // Provide more specific error information
    if (error?.code === 50001) {
      logger.error('[AutoDeploy] Missing access - check bot permissions');
    } else if (error?.code === 50013) {
      logger.error('[AutoDeploy] Missing permissions - bot needs application.commands scope');
    } else if (error?.status === 401) {
      logger.error('[AutoDeploy] Invalid bot token');
    } else if (error?.status === 429) {
      logger.error('[AutoDeploy] Rate limited - too many requests');
    }

    return false;
  }
}

/**
 * Validate environment variables required for deployment
 */
export function validateEnvironment(): { valid: boolean; missing: string[] } {
  const required = ['BOT_TOKEN', 'CLIENT_ID', 'MONGODB_URI'];
  const missing = required.filter(key => !process.env[key]);

  return {
    valid: missing.length === 0,
    missing
  };
}

/**
 * Check if all required dependencies are available
 */
export async function checkDependencies(): Promise<{ valid: boolean; issues: string[] }> {
  const issues: string[] = [];

  try {
    // Check if TypeScript is available
    const tscPath = path.join(process.cwd(), 'node_modules', '.bin', 'tsc');
    if (!fs.existsSync(tscPath)) {
      issues.push('TypeScript compiler not found in node_modules');
    }

    // Check if Discord.js is available
    try {
      require('discord.js');
    } catch (error) {
      issues.push('Discord.js not found or not properly installed');
    }

    // Check if source files exist
    const srcExists = fs.existsSync(path.join(process.cwd(), 'src'));
    if (!srcExists) {
      issues.push('Source directory (src/) not found');
    }

    // Check if package.json exists
    const packageJsonExists = fs.existsSync(path.join(process.cwd(), 'package.json'));
    if (!packageJsonExists) {
      issues.push('package.json not found');
    }

  } catch (error: any) {
    issues.push(`Dependency check failed: ${error?.message || 'Unknown error'}`);
  }

  return {
    valid: issues.length === 0,
    issues
  };
}
