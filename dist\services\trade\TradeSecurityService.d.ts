/**
 * Trade Security Service
 * Handles security, anti-abuse, and rate limiting for the trade system
 */
import { BaseService } from '../base/BaseService';
import { ITrade } from '../../models';
export interface RateLimitCheck {
    allowed: boolean;
    reason?: string;
    resetTime?: Date;
    remaining?: number;
}
export interface SecurityCheck {
    passed: boolean;
    violations: string[];
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    recommendations: string[];
}
export interface UserRiskProfile {
    discordId: string;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    factors: string[];
    score: number;
    lastUpdated: Date;
}
/**
 * Trade Security Service Class
 */
export declare class TradeSecurityService extends BaseService {
    private rateLimitCache;
    private suspiciousActivityCache;
    constructor(app: any);
    /**
     * Initialize the security service
     */
    onInitialize(): Promise<void>;
    /**
     * Check if user can initiate a trade (rate limiting)
     */
    checkTradeRateLimit(discordId: string, guildId: string): Promise<RateLimitCheck>;
    /**
     * Perform comprehensive security check on a trade
     */
    performSecurityCheck(trade: ITrade): Promise<SecurityCheck>;
    /**
     * Get user risk profile
     */
    getUserRiskProfile(discordId: string, guildId: string): Promise<UserRiskProfile>;
    /**
     * Apply automatic restrictions based on user behavior
     */
    applyAutomaticRestrictions(discordId: string, guildId: string): Promise<boolean>;
    private detectSuspiciousPatterns;
    private checkTradeAmount;
    private checkRapidTrading;
    private cleanupRateLimitCache;
    private getNextMidnight;
}
//# sourceMappingURL=TradeSecurityService.d.ts.map