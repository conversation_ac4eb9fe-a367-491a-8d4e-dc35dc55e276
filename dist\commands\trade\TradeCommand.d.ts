/**
 * Trade Command
 * Main command for handling all trade operations
 */
import { SlashCommandBuilder } from 'discord.js';
import { BaseCommand } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { TradeService } from '../../services/trade';
/**
 * Trade command implementation
 */
export declare class TradeCommand extends BaseCommand {
    private tradeService;
    constructor();
    /**
     * Set the trade service (dependency injection)
     */
    setTradeService(tradeService: TradeService): void;
    /**
     * Customize the command builder with subcommands
     */
    protected customizeCommand(command: SlashCommandBuilder): void;
    /**
     * Execute the trade command
     */
    protected executeCommand(context: CommandContext): Promise<void>;
    /**
     * Handle sell subcommand
     */
    private handleSell;
    /**
     * Handle buy subcommand
     */
    private handleBuy;
    /**
     * Handle status subcommand
     */
    private handleStatus;
    /**
     * Handle history subcommand
     */
    private handleHistory;
    /**
     * <PERSON>le cancel subcommand
     */
    private handleCancel;
    /**
     * <PERSON>le confirm subcommand
     */
    private handleConfirm;
    /**
     * <PERSON>le dispute subcommand
     */
    private handleDispute;
    /**
     * Handle help subcommand
     */
    private handleHelp;
}
//# sourceMappingURL=TradeCommand.d.ts.map