{"version": 3, "file": "UserTradeStats.js", "sourceRoot": "", "sources": ["../../src/models/UserTradeStats.ts"], "names": [], "mappings": ";;AAAA,uCAAmD;AACnD,mDAA4C;AAqD5C,MAAM,oBAAoB,GAAG,IAAI,iBAAM,CAAkB;IACvD,SAAS,EAAE;QACT,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,wBAAwB,CAAC;QAC1C,QAAQ,EAAE;YACR,SAAS,EAAE,UAAS,CAAS;gBAC3B,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,EAAE,8CAA8C;SACxD;QACD,KAAK,EAAE,IAAI;KACZ;IACD,OAAO,EAAE;QACP,IAAI,EAAE,MAAM;QACZ,QAAQ,EAAE,CAAC,IAAI,EAAE,sBAAsB,CAAC;QACxC,QAAQ,EAAE;YACR,SAAS,EAAE,UAAS,CAAS;gBAC3B,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC/B,CAAC;YACD,OAAO,EAAE,4CAA4C;SACtD;QACD,KAAK,EAAE,IAAI;KACZ;IACD,WAAW,EAAE;QACX,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,iCAAiC,CAAC;KAC5C;IACD,gBAAgB,EAAE;QAChB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,sCAAsC,CAAC;KACjD;IACD,eAAe,EAAE;QACf,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,qCAAqC,CAAC;KAChD;IACD,aAAa,EAAE;QACb,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,mCAAmC,CAAC;KAC9C;IACD,cAAc,EAAE;QACd,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,oCAAoC,CAAC;KAC/C;IACD,cAAc,EAAE;QACd,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,qCAAqC,CAAC;KAChD;IACD,aAAa,EAAE;QACb,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,oCAAoC,CAAC;KAC/C;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,wCAAwC,CAAC;KACnD;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,wCAAwC,CAAC;KACnD;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,kCAAkC,CAAC;KAC7C;IACD,eAAe,EAAE;QACf,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,EAAE,EAAE,gCAAgC;QAC7C,GAAG,EAAE,CAAC,CAAC,EAAE,oCAAoC,CAAC;QAC9C,GAAG,EAAE,CAAC,GAAG,EAAE,sCAAsC,CAAC;QAClD,KAAK,EAAE,IAAI;KACZ;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,kCAAkC,CAAC;QAC5C,GAAG,EAAE,CAAC,CAAC,EAAE,iCAAiC,CAAC;QAC3C,KAAK,EAAE,IAAI;KACZ;IACD,cAAc,EAAE;QACd,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,oCAAoC,CAAC;QAC9C,GAAG,EAAE,CAAC,CAAC,EAAE,mCAAmC,CAAC;KAC9C;IACD,qBAAqB,EAAE;QACrB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,4CAA4C,CAAC;KACvD;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,uCAAuC,CAAC;KAClD;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,kCAAkC,CAAC;QAC5C,GAAG,EAAE,CAAC,iBAAK,CAAC,0BAA0B,EAAE,yBAAyB,iBAAK,CAAC,0BAA0B,gBAAgB,CAAC;QAClH,KAAK,EAAE,IAAI;KACZ;IACD,YAAY,EAAE;QACZ,IAAI,EAAE,OAAO;QACb,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,IAAI;KACZ;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,MAAM;QACZ,SAAS,EAAE,CAAC,GAAG,EAAE,iDAAiD,CAAC;KACpE;IACD,eAAe,EAAE;QACf,IAAI,EAAE,IAAI;QACV,KAAK,EAAE,IAAI;KACZ;IACD,eAAe,EAAE;QACf,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,sCAAsC,CAAC;QAChD,GAAG,EAAE,CAAC,iBAAK,CAAC,2BAA2B,EAAE,iBAAiB,iBAAK,CAAC,2BAA2B,iBAAiB,CAAC;KAC9G;IACD,aAAa,EAAE;QACb,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI,CAAC,GAAG;QACjB,KAAK,EAAE,IAAI;KACZ;IACD,aAAa,EAAE;QACb,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI,CAAC,GAAG;KAClB;IACD,cAAc,EAAE;QACd,IAAI,EAAE,IAAI;KACX;IACD,WAAW,EAAE;QACX,IAAI,EAAE,IAAI;QACV,OAAO,EAAE,IAAI,CAAC,GAAG;QACjB,KAAK,EAAE,IAAI;KACZ;IACD,gBAAgB,EAAE;QAChB,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,CAAC;QACV,GAAG,EAAE,CAAC,CAAC,EAAE,sCAAsC,CAAC;KACjD;IACD,eAAe,EAAE;QACf,IAAI,EAAE,IAAI;KACX;IACD,gBAAgB,EAAE;QAChB,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE;YACR,SAAS,EAAE,UAAS,CAAW;gBAC7B,OAAO,CAAC,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,2BAA2B;YACpD,CAAC;YACD,OAAO,EAAE,4CAA4C;SACtD;KACF;CACF,EAAE;IACD,UAAU,EAAE,KAAK,CAAC,gCAAgC;CACnD,CAAC,CAAC;AAEH,yCAAyC;AACzC,oBAAoB,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3E,oBAAoB,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAChE,oBAAoB,CAAC,KAAK,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AAClE,oBAAoB,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,CAAC,EAAE,eAAe,EAAE,CAAC,EAAE,CAAC,CAAC;AAEpE,6BAA6B;AAC7B,oBAAoB,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;IAC9B,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC;AAEH,oCAAoC;AACpC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,GAAG;IACtC,sBAAsB;IACtB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;QACtB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;YAC9D,iCAAiC;YACjC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACjC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,QAAQ,EAAE,KAAK;gBACf,MAAM,EAAE,IAAI,CAAC,iBAAiB,IAAI,oCAAoC;aACvE,CAAC;QACJ,CAAC;IACH,CAAC;IAED,0BAA0B;IAC1B,IAAI,CAAC,uBAAuB,EAAE,CAAC;IAC/B,IAAI,IAAI,CAAC,eAAe,IAAI,iBAAK,CAAC,2BAA2B,EAAE,CAAC;QAC9D,OAAO;YACL,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,wBAAwB,iBAAK,CAAC,2BAA2B,UAAU;SAC5E,CAAC;IACJ,CAAC;IAED,2BAA2B;IAC3B,IAAI,IAAI,CAAC,YAAY,IAAI,iBAAK,CAAC,0BAA0B,EAAE,CAAC;QAC1D,OAAO;YACL,QAAQ,EAAE,KAAK;YACf,MAAM,EAAE,cAAc,iBAAK,CAAC,0BAA0B,wBAAwB;SAC/E,CAAC;IACJ,CAAC;IAED,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;AAC5B,CAAC,CAAC;AAEF,wCAAwC;AACxC,oBAAoB,CAAC,OAAO,CAAC,uBAAuB,GAAG;IACrD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE/C,0BAA0B;IAC1B,IAAI,GAAG,CAAC,OAAO,EAAE,KAAK,SAAS,CAAC,OAAO,EAAE;QACrC,GAAG,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,QAAQ,EAAE;QACvC,GAAG,CAAC,WAAW,EAAE,KAAK,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;QAClD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,GAAG,CAAC;IAC3B,CAAC;AACH,CAAC,CAAC;AAEF,uCAAuC;AACvC,oBAAoB,CAAC,OAAO,CAAC,wBAAwB,GAAG;IACtD,IAAI,IAAI,CAAC,WAAW,GAAG,iBAAK,CAAC,yBAAyB,EAAE,CAAC;QACvD,OAAO,EAAE,CAAC,CAAC,8BAA8B;IAC3C,CAAC;IAED,IAAI,KAAK,GAAG,EAAE,CAAC,CAAC,qBAAqB;IAErC,mBAAmB;IACnB,KAAK,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC,CAAC,qCAAqC;IACxE,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,6BAA6B;IAE3E,mBAAmB;IACnB,KAAK,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC,mCAAmC;IACpE,KAAK,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,iBAAiB;IAErD,0BAA0B;IAC1B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACvD,CAAC,CAAC;AAEF,gDAAgD;AAChD,oBAAoB,CAAC,OAAO,CAAC,gBAAgB,GAAG,UAC9C,WAAmB,EACnB,mBAA2B,EAC3B,aAAsB,EACtB,WAAoB,EACpB,QAAiB;IAEjB,IAAI,CAAC,WAAW,EAAE,CAAC;IACnB,IAAI,CAAC,iBAAiB,IAAI,WAAW,CAAC;IACtC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;IAEvD,IAAI,QAAQ,EAAE,CAAC;QACb,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;SAAM,CAAC;QACN,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,IAAI,aAAa,EAAE,CAAC;QAClB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAExB,sBAAsB;QACtB,IAAI,IAAI,CAAC,qBAAqB,KAAK,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,qBAAqB,GAAG,CAAC,IAAI,CAAC,qBAAqB,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;QACtF,CAAC;QAED,IAAI,IAAI,CAAC,iBAAiB,KAAK,CAAC,IAAI,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACjF,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QAChB,IAAI,CAAC,cAAc,EAAE,CAAC;IACxB,CAAC;IAED,uBAAuB;IACvB,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;IAClC,CAAC;IAED,4BAA4B;IAC5B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,WAAW,CAAC;IACnE,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC;IAC3D,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;IAC/D,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAEvD,kCAAkC;IAClC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,IAAI,EAAE,CAAC;IACnC,CAAC;IAED,IAAI,CAAC,aAAa,GAAG,IAAI,IAAI,EAAE,CAAC;AAClC,CAAC,CAAC;AAEF,kBAAe,IAAA,gBAAK,EAAkB,gBAAgB,EAAE,oBAAoB,CAAC,CAAC"}