/**
 * Trade Monitor Command
 * Real-time monitoring and management for the trade system
 */

import { S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, ChatInputCommandInteraction, PermissionFlagsBits, EmbedBuilder } from 'discord.js';
import { BaseCommand, CommandCategory } from '../base/BaseCommand';
import { CommandContext } from '../../core/interfaces';
import { createErrorEmbed, createSuccessEmbed, createAdminEmbed, formatCoins, EMOJIS, COLORS } from '../../utils/embedBuilder';
import { ValidationError } from '../../utils/errorHandler';
import { TradeService } from '../../services/trade/TradeService';
import { DisputeService } from '../../services/trade/DisputeService';
import { TradeBackgroundService } from '../../services/trade/TradeBackgroundService';
import { Trade, UserTradeStats, EscrowTransaction, DisputeCase } from '../../models';

/**
 * Trade Monitor command implementation
 */
export class TradeMonitorCommand extends BaseCommand {
  private tradeService: TradeService;
  private disputeService: DisputeService;
  private backgroundService: TradeBackgroundService;

  constructor() {
    super({
      name: 'trademonitor',
      description: 'Real-time monitoring and management for the trade system',
      category: CommandCategory.ADMIN,
      requiredFeatures: ['TRADE_SYSTEM'],
      adminOnly: true,
      cooldown: 5,
    });
    
    // Services will be injected when the command is registered
    this.tradeService = null as any;
    this.disputeService = null as any;
    this.backgroundService = null as any;
  }

  /**
   * Set the trade services (dependency injection)
   */
  setTradeServices(tradeService: TradeService, disputeService: DisputeService, backgroundService: TradeBackgroundService): void {
    this.tradeService = tradeService;
    this.disputeService = disputeService;
    this.backgroundService = backgroundService;
  }

  /**
   * Customize the command builder with subcommands
   */
  protected customizeCommand(command: SlashCommandBuilder): void {
    command
      .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
      .addSubcommand(subcommand =>
        subcommand
          .setName('active')
          .setDescription('View all active trades')
          .addIntegerOption(option =>
            option.setName('limit')
              .setDescription('Number of trades to show (default: 20)')
              .setRequired(false)
              .setMinValue(1)
              .setMaxValue(50)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('stuck')
          .setDescription('Find potentially stuck or problematic trades'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('escrow')
          .setDescription('Check escrow balance consistency'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('cleanup')
          .setDescription('Manually trigger cleanup operations'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('health')
          .setDescription('Perform comprehensive system health check'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('user')
          .setDescription('Deep dive into a user\'s trade activity')
          .addUserOption(option =>
            option.setName('user')
              .setDescription('User to analyze')
              .setRequired(true)))
      .addSubcommand(subcommand =>
        subcommand
          .setName('alerts')
          .setDescription('View system alerts and warnings'))
      .addSubcommand(subcommand =>
        subcommand
          .setName('performance')
          .setDescription('View system performance metrics'));
  }

  /**
   * Execute the trade monitor command
   */
  protected async executeCommand(context: CommandContext): Promise<void> {
    const { interaction } = context;
    
    if (!this.tradeService || !this.disputeService || !this.backgroundService) {
      throw new Error('Trade services not initialized');
    }

    const subcommand = interaction.options.getSubcommand();

    try {
      switch (subcommand) {
        case 'active':
          await this.handleActive(interaction);
          break;
        case 'stuck':
          await this.handleStuck(interaction);
          break;
        case 'escrow':
          await this.handleEscrow(interaction);
          break;
        case 'cleanup':
          await this.handleCleanup(interaction);
          break;
        case 'health':
          await this.handleHealth(interaction);
          break;
        case 'user':
          await this.handleUser(interaction);
          break;
        case 'alerts':
          await this.handleAlerts(interaction);
          break;
        case 'performance':
          await this.handlePerformance(interaction);
          break;
        default:
          throw new ValidationError(`Unknown subcommand: ${subcommand}`);
      }
    } catch (error) {
      this.logger.error(`Error executing trademonitor ${subcommand}`, { error, userId: interaction.user.id });
      throw error;
    }
  }

  /**
   * Handle active trades monitoring
   */
  private async handleActive(interaction: ChatInputCommandInteraction): Promise<void> {
    const limit = interaction.options.getInteger('limit') || 20;
    
    if (!interaction.guild) {
      throw new ValidationError('This command can only be used in a server');
    }

    await interaction.deferReply();

    const activeTrades = await Trade.find({
      guildId: interaction.guild.id,
      state: { $in: ['PROPOSED', 'ACCEPTED', 'ACTIVE'] }
    })
    .sort({ createdAt: -1 })
    .limit(limit)
    .lean();

    if (activeTrades.length === 0) {
      const embed = createAdminEmbed('No Active Trades', 'There are currently no active trades.');
      await interaction.editReply({ embeds: [embed] });
      return;
    }

    const embed = createAdminEmbed(
      `${EMOJIS.TRADE.ACTIVE} Active Trades`,
      `Found ${activeTrades.length} active trade(s)`
    );

    for (const trade of activeTrades.slice(0, 10)) { // Limit for embed space
      const stateEmoji = trade.state === 'PROPOSED' ? '🟡' : 
                        trade.state === 'ACCEPTED' ? '🔵' : '🟢';
      
      const timeRemaining = Math.max(0, trade.expiresAt.getTime() - Date.now());
      const hoursRemaining = Math.floor(timeRemaining / (1000 * 60 * 60));
      
      embed.addFields([{
        name: `${stateEmoji} ${trade.tradeId}`,
        value: 
          `**State:** ${trade.state}\n` +
          `**Amount:** ${formatCoins(trade.amount)}\n` +
          `**Expires:** ${hoursRemaining > 0 ? `${hoursRemaining}h` : 'EXPIRED'}\n` +
          `**Escrow:** ${trade.escrowLocked ? '🔒 Locked' : '🔓 Unlocked'}`,
        inline: true
      }]);
    }

    if (activeTrades.length > 10) {
      embed.setFooter({ text: `Showing first 10 of ${activeTrades.length} active trades` });
    }

    await interaction.editReply({ embeds: [embed] });
  }

  /**
   * Handle stuck trades detection
   */
  private async handleStuck(interaction: ChatInputCommandInteraction): Promise<void> {
    if (!interaction.guild) {
      throw new ValidationError('This command can only be used in a server');
    }

    await interaction.deferReply();

    // Find potentially stuck trades
    const stuckTrades = await Trade.find({
      guildId: interaction.guild.id,
      state: 'ACTIVE',
      escrowLocked: true,
      expiresAt: { $gt: new Date() }, // Not expired
      $or: [
        { 
          sellerConfirmed: true, 
          buyerConfirmed: false, 
          sellerConfirmedAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
        },
        { 
          sellerConfirmed: false, 
          buyerConfirmed: true, 
          buyerConfirmedAt: { $lt: new Date(Date.now() - 24 * 60 * 60 * 1000) } 
        }
      ]
    }).lean();

    if (stuckTrades.length === 0) {
      const embed = createSuccessEmbed('No Stuck Trades', 'All trades appear to be progressing normally.');
      await interaction.editReply({ embeds: [embed] });
      return;
    }

    const embed = createAdminEmbed(
      `${EMOJIS.ADMIN.WARNING} Potentially Stuck Trades`,
      `Found ${stuckTrades.length} trade(s) that may need attention`
    );

    for (const trade of stuckTrades) {
      const stuckHours = Math.floor((Date.now() - Math.max(
        trade.sellerConfirmedAt?.getTime() || 0,
        trade.buyerConfirmedAt?.getTime() || 0
      )) / (1000 * 60 * 60));

      embed.addFields([{
        name: `⚠️ ${trade.tradeId}`,
        value: 
          `**Amount:** ${formatCoins(trade.amount)}\n` +
          `**Seller Confirmed:** ${trade.sellerConfirmed ? '✅' : '❌'}\n` +
          `**Buyer Confirmed:** ${trade.buyerConfirmed ? '✅' : '❌'}\n` +
          `**Stuck for:** ${stuckHours} hours`,
        inline: true
      }]);
    }

    await interaction.editReply({ embeds: [embed] });
  }

  /**
   * Handle escrow consistency check
   */
  private async handleEscrow(interaction: ChatInputCommandInteraction): Promise<void> {
    if (!interaction.guild) {
      throw new ValidationError('This command can only be used in a server');
    }

    await interaction.deferReply();

    // Check for escrow inconsistencies
    const inconsistentTrades = await Trade.find({
      guildId: interaction.guild.id,
      escrowLocked: true,
      escrowAmount: { $gt: 0 },
      state: { $in: ['COMPLETED', 'CANCELLED', 'EXPIRED'] }
    }).lean();

    // Get total escrow amounts
    const totalEscrowLocked = await Trade.aggregate([
      { 
        $match: { 
          guildId: interaction.guild.id, 
          escrowLocked: true, 
          escrowAmount: { $gt: 0 } 
        } 
      },
      { $group: { _id: null, total: { $sum: '$escrowAmount' } } }
    ]);

    const embed = createAdminEmbed(
      `${EMOJIS.TRADE.ESCROW} Escrow System Status`,
      'Escrow balance and consistency check'
    );

    embed.addFields([
      {
        name: `${EMOJIS.ECONOMY.COINS} Escrow Summary`,
        value: 
          `**Total Locked:** ${formatCoins(totalEscrowLocked[0]?.total || 0)}\n` +
          `**Inconsistencies:** ${inconsistentTrades.length}\n` +
          `**Status:** ${inconsistentTrades.length === 0 ? '🟢 Healthy' : '🔴 Issues Found'}`,
        inline: true
      }
    ]);

    if (inconsistentTrades.length > 0) {
      const inconsistencyText = inconsistentTrades.slice(0, 5).map(trade => 
        `\`${trade.tradeId}\` - ${trade.state} (${formatCoins(trade.escrowAmount)})`
      ).join('\n');

      embed.addFields([{
        name: `${EMOJIS.ADMIN.WARNING} Inconsistencies Found`,
        value: inconsistencyText + (inconsistentTrades.length > 5 ? `\n...and ${inconsistentTrades.length - 5} more` : ''),
        inline: false
      }]);
    }

    await interaction.editReply({ embeds: [embed] });
  }

  /**
   * Handle manual cleanup trigger
   */
  private async handleCleanup(interaction: ChatInputCommandInteraction): Promise<void> {
    await interaction.deferReply();

    try {
      // Trigger background service cleanup
      const cleanupCount = await this.backgroundService.performCleanupOperations();
      
      const embed = createSuccessEmbed('Cleanup Completed')
        .setDescription(
          `${EMOJIS.SUCCESS.CHECK} **Manual Cleanup Executed**\n\n` +
          `**Operations Performed:** ${cleanupCount}\n` +
          `**Triggered by:** ${interaction.user.displayName}\n` +
          `**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>`
        );

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      const embed = createErrorEmbed(
        'Cleanup Failed',
        'Failed to perform cleanup operations. Check logs for details.'
      );
      await interaction.editReply({ embeds: [embed] });
    }
  }

  /**
   * Handle comprehensive health check
   */
  private async handleHealth(interaction: ChatInputCommandInteraction): Promise<void> {
    await interaction.deferReply();

    try {
      // Perform health check
      await this.backgroundService.performHealthCheck();
      
      // Get background service stats
      const stats = this.backgroundService.getTaskStats();
      
      const embed = createSuccessEmbed('System Health Check')
        .setDescription(
          `${EMOJIS.SUCCESS.CHECK} **Health Check Completed**\n\n` +
          `**Background Tasks:**\n` +
          `• Expired Trades: ${stats.expiredTrades}\n` +
          `• Warnings Sent: ${stats.warningsSent}\n` +
          `• Cleanup Operations: ${stats.cleanupOperations}\n` +
          `• Errors: ${stats.errors}\n\n` +
          `**Last Run:** <t:${Math.floor(stats.lastRun.getTime() / 1000)}:R>\n` +
          `**System Status:** ${stats.errors < 10 ? '🟢 Healthy' : '🟡 Warning'}`
        );

      await interaction.editReply({ embeds: [embed] });

    } catch (error) {
      const embed = createErrorEmbed(
        'Health Check Failed',
        'Failed to perform system health check. Check logs for details.'
      );
      await interaction.editReply({ embeds: [embed] });
    }
  }

  /**
   * Handle user analysis
   */
  private async handleUser(interaction: ChatInputCommandInteraction): Promise<void> {
    const user = interaction.options.getUser('user', true);
    
    if (!interaction.guild) {
      throw new ValidationError('This command can only be used in a server');
    }

    await interaction.deferReply();

    // Get user's recent trades
    const recentTrades = await Trade.find({
      guildId: interaction.guild.id,
      $or: [{ sellerId: user.id }, { buyerId: user.id }]
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .lean();

    // Get user stats
    const userStats = await UserTradeStats.findOne({
      discordId: user.id,
      guildId: interaction.guild.id
    });

    const embed = createAdminEmbed(
      `${EMOJIS.MISC.USER} User Trade Analysis`,
      `Analysis for ${user.displayName}`
    );

    if (!userStats) {
      embed.setDescription(`${user.displayName} has no trade history.`);
      await interaction.editReply({ embeds: [embed] });
      return;
    }

    embed.addFields([
      {
        name: `${EMOJIS.ECONOMY.CHART} Statistics`,
        value: 
          `**Total Trades:** ${userStats.totalTrades}\n` +
          `**Success Rate:** ${(userStats.completionRate * 100).toFixed(1)}%\n` +
          `**Dispute Rate:** ${(userStats.disputeRatio * 100).toFixed(1)}%\n` +
          `**Reputation:** ${userStats.reputationScore}/100`,
        inline: true
      },
      {
        name: `${EMOJIS.ADMIN.WARNING} Risk Factors`,
        value: 
          `**Restricted:** ${userStats.isRestricted ? 'Yes' : 'No'}\n` +
          `**Warnings:** ${userStats.warningsReceived}\n` +
          `**Violations:** ${userStats.violationHistory.length}\n` +
          `**Active Trades:** ${userStats.activeTrades}`,
        inline: true
      }
    ]);

    if (recentTrades.length > 0) {
      const recentTradesText = recentTrades.slice(0, 5).map(trade => {
        const role = trade.sellerId === user.id ? 'Seller' : 'Buyer';
        const stateEmoji = trade.state === 'COMPLETED' ? '✅' : 
                          trade.state === 'DISPUTED' ? '⚠️' : 
                          trade.state === 'CANCELLED' ? '❌' : '🔄';
        return `${stateEmoji} \`${trade.tradeId}\` - ${role} (${formatCoins(trade.amount)})`;
      }).join('\n');

      embed.addFields([{
        name: `${EMOJIS.MISC.CLOCK} Recent Trades`,
        value: recentTradesText,
        inline: false
      }]);
    }

    await interaction.editReply({ embeds: [embed] });
  }

  /**
   * Handle alerts view
   */
  private async handleAlerts(interaction: ChatInputCommandInteraction): Promise<void> {
    const embed = createAdminEmbed('System Alerts', 'Advanced alerting system will be implemented in future updates.');
    await interaction.reply({ embeds: [embed] });
  }

  /**
   * Handle performance metrics
   */
  private async handlePerformance(interaction: ChatInputCommandInteraction): Promise<void> {
    const embed = createAdminEmbed('Performance Metrics', 'Performance monitoring will be implemented in future updates.');
    await interaction.reply({ embeds: [embed] });
  }
}
