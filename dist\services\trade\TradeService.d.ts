/**
 * Trade Service
 * Core service for managing secure trades between users
 */
import { Client } from 'discord.js';
import { BaseService } from '../base/BaseService';
import { ITrade } from '../../models';
export interface TradeCreationParams {
    sellerId: string;
    buyerId: string;
    guildId: string;
    amount: number;
    itemDescription: string;
    notes?: string;
    initiatedBy: 'SELLER' | 'BUYER';
}
export interface TradeStateTransition {
    tradeId: string;
    fromState: string;
    toState: string;
    triggeredBy: string;
    reason?: string;
}
/**
 * Main Trade Service Class
 */
export declare class TradeService extends BaseService {
    private escrowManager;
    private validator;
    private notificationManager;
    private securityService;
    constructor(app: any);
    /**
     * Initialize the trade service
     */
    onInitialize(): Promise<void>;
    /**
     * Create a new trade proposal
     */
    createTrade(params: TradeCreationParams, client?: Client): Promise<ITrade>;
    /**
     * Accept a trade proposal
     */
    acceptTrade(tradeId: string, acceptingUserId: string, client?: Client): Promise<ITrade>;
    /**
     * Confirm trade completion by a party
     */
    confirmTrade(tradeId: string, confirmingUserId: string, client?: Client): Promise<{
        trade: ITrade;
        completed: boolean;
    }>;
    /**
     * Cancel a trade
     */
    cancelTrade(tradeId: string, cancellingUserId: string, reason?: string, client?: Client): Promise<ITrade>;
    /**
     * Get trade by ID
     */
    getTrade(tradeId: string): Promise<ITrade | null>;
    /**
     * Get user's active trades
     */
    getUserActiveTrades(discordId: string, guildId?: string): Promise<ITrade[]>;
    private transitionToActive;
    private completeTrade;
    private updateUserTradeStats;
    private generateTradeId;
    private generateConfirmationId;
}
//# sourceMappingURL=TradeService.d.ts.map