import { Document } from 'mongoose';
export interface IUserTradeStats extends Document {
    discordId: string;
    guildId: string;
    totalTrades: number;
    successfulTrades: number;
    cancelledTrades: number;
    expiredTrades: number;
    disputedTrades: number;
    tradesAsSeller: number;
    tradesAsBuyer: number;
    totalVolumeTraded: number;
    averageTradeValue: number;
    largestTrade: number;
    reputationScore: number;
    disputeRatio: number;
    completionRate: number;
    averageCompletionTime: number;
    fastestCompletion: number;
    activeTrades: number;
    isRestricted: boolean;
    restrictionReason?: string;
    restrictedUntil?: Date;
    dailyTradeCount: number;
    lastTradeDate: Date;
    lastResetDate: Date;
    firstTradeDate?: Date;
    lastUpdated: Date;
    warningsReceived: number;
    lastWarningDate?: Date;
    violationHistory: string[];
}
declare const _default: import("mongoose").Model<IUserTradeStats, {}, {}, {}, Document<unknown, {}, IUserTradeStats, {}> & IUserTradeStats & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=UserTradeStats.d.ts.map