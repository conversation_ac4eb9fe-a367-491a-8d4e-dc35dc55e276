/**
 * Trade Service Module Index
 * Centralized exports for the trade system
 */
export { TradeService, TradeCreationParams, TradeStateTransition } from './TradeService';
export { EscrowManager, EscrowOperation } from './managers/EscrowManager';
export { TradeValidator } from './managers/TradeValidator';
export { TradeNotificationManager } from './managers/TradeNotificationManager';
export { Trade, ITrade, EscrowTransaction, IEscrowTransaction, DisputeCase, IDisputeCase, TradeConfirmation, ITradeConfirmation, UserTradeStats, IUserTradeStats, TradeState, EscrowTransactionType, DisputeStatus, DisputeResolution, ConfirmationType } from '../../models';
export { TRADE } from '../../config/constants';
//# sourceMappingURL=index.d.ts.map