// Quick debug script to check trade eligibility
const mongoose = require('mongoose');
const dotenv = require('dotenv');

dotenv.config();

// Define schemas
const userTradeStatsSchema = new mongoose.Schema({
  discordId: String,
  guildId: String,
  totalTrades: { type: Number, default: 0 },
  successfulTrades: { type: Number, default: 0 },
  cancelledTrades: { type: Number, default: 0 },
  expiredTrades: { type: Number, default: 0 },
  disputedTrades: { type: Number, default: 0 },
  tradesAsSeller: { type: Number, default: 0 },
  tradesAsBuyer: { type: Number, default: 0 },
  totalVolumeTraded: { type: Number, default: 0 },
  averageTradeValue: { type: Number, default: 0 },
  largestTrade: { type: Number, default: 0 },
  reputationScore: { type: Number, default: 50 },
  disputeRatio: { type: Number, default: 0 },
  completionRate: { type: Number, default: 0 },
  averageCompletionTime: { type: Number, default: 0 },
  fastestCompletion: { type: Number, default: 0 },
  activeTrades: { type: Number, default: 0 },
  isRestricted: { type: Boolean, default: false },
  restrictionReason: String,
  restrictedUntil: Date,
  dailyTradeCount: { type: Number, default: 0 },
  lastTradeDate: { type: Date, default: Date.now },
  lastResetDate: { type: Date, default: Date.now },
  firstTradeDate: Date,
  lastUpdated: { type: Date, default: Date.now },
  warningsReceived: { type: Number, default: 0 },
  lastWarningDate: Date,
  violationHistory: { type: [String], default: [] }
});

// Method to check if user can trade
userTradeStatsSchema.methods.canTrade = function() {
  console.log('Checking trade eligibility:', {
    isRestricted: this.isRestricted,
    dailyTradeCount: this.dailyTradeCount,
    activeTrades: this.activeTrades
  });

  // Check if restricted
  if (this.isRestricted) {
    if (this.restrictedUntil && new Date() > this.restrictedUntil) {
      // Restriction expired, remove it
      this.isRestricted = false;
      this.restrictedUntil = undefined;
      this.restrictionReason = undefined;
    } else {
      return { 
        canTrade: false, 
        reason: this.restrictionReason || 'Account is restricted from trading' 
      };
    }
  }
  
  // Check daily trade limit (max 10)
  if (this.dailyTradeCount >= 10) {
    return { 
      canTrade: false, 
      reason: `Daily trade limit of 10 reached` 
    };
  }
  
  // Check active trade limit (max 5)
  if (this.activeTrades >= 5) {
    return { 
      canTrade: false, 
      reason: `Maximum of 5 active trades allowed` 
    };
  }
  
  return { canTrade: true };
};

const UserTradeStats = mongoose.model('UserTradeStats', userTradeStatsSchema);

async function checkUserEligibility() {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');
    
    // Replace with your actual Discord user ID and guild ID
    const testUserId = '123456789012345678';  // Replace with real user ID
    const testGuildId = '987654321098765432'; // Replace with real guild ID
    
    let userStats = await UserTradeStats.findOne({ 
      discordId: testUserId, 
      guildId: testGuildId 
    });
    
    if (!userStats) {
      console.log('No user stats found, creating new...');
      userStats = new UserTradeStats({
        discordId: testUserId,
        guildId: testGuildId
      });
      await userStats.save();
      console.log('Created new user stats');
    } else {
      console.log('Found existing user stats:', userStats.toObject());
    }
    
    const eligibility = userStats.canTrade();
    console.log('Trade eligibility result:', eligibility);
    
    if (eligibility.canTrade) {
      console.log('✅ User can trade!');
    } else {
      console.log('❌ User cannot trade:', eligibility.reason);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

checkUserEligibility();
