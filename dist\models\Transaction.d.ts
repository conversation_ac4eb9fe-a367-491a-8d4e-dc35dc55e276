import mongoose, { Document } from 'mongoose';
export interface ITransaction extends Document {
    discordId: string;
    type: 'pay' | 'role_achievement' | 'give' | 'fine' | 'reaction' | 'tax' | 'starter_balance' | 'content_submission' | 'content_reward' | 'milestone' | 'trade_escrow' | 'trade_release' | 'trade_refund';
    amount: number;
    timestamp: Date;
    details?: string;
    tradeId?: string;
}
declare const _default: mongoose.Model<ITransaction, {}, {}, {}, mongoose.Document<unknown, {}, ITransaction, {}> & ITransaction & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>;
export default _default;
//# sourceMappingURL=Transaction.d.ts.map