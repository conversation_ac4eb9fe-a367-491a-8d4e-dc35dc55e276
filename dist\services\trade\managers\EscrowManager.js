"use strict";
/**
 * Escrow Manager
 * Handles secure escrow operations for trades
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EscrowManager = void 0;
const mongoose_1 = __importDefault(require("mongoose"));
const BaseService_1 = require("../../base/BaseService");
const decorators_1 = require("../../../core/decorators");
const errorHandler_1 = require("../../../utils/errorHandler");
const models_1 = require("../../../models");
/**
 * Escrow Manager Class
 */
class EscrowManager extends BaseService_1.BaseService {
    constructor(app) {
        super('EscrowManager', app);
    }
    /**
     * Initialize the escrow manager
     */
    async initialize() {
        this.logger.info('[EscrowManager] Escrow manager initialized');
    }
    /**
     * Lock funds in escrow for a trade
     */
    async lockEscrow(trade, session) {
        this.logOperation('Locking escrow funds', {
            tradeId: trade.tradeId,
            buyerId: trade.buyerId,
            amount: trade.amount
        });
        try {
            // Verify buyer has sufficient balance
            const buyer = await models_1.User.findOne({ discordId: trade.buyerId }).session(session);
            if (!buyer) {
                throw new errorHandler_1.ValidationError('Buyer not found');
            }
            if (buyer.balance < trade.amount) {
                throw new errorHandler_1.ValidationError(`Insufficient balance. Required: ${trade.amount}, Available: ${buyer.balance}`);
            }
            // Deduct from buyer's balance
            buyer.balance -= trade.amount;
            await buyer.save({ session });
            // Create main transaction record
            const transactionId = this.generateTransactionId();
            await models_1.Transaction.create([{
                    discordId: trade.buyerId,
                    type: 'trade_escrow',
                    amount: -trade.amount,
                    details: `Escrow locked for trade ${trade.tradeId}`,
                    tradeId: trade.tradeId,
                    timestamp: new Date()
                }], { session });
            // Create escrow transaction record
            const escrowTransaction = await models_1.EscrowTransaction.create([{
                    escrowId: this.generateEscrowId(),
                    tradeId: trade.tradeId,
                    discordId: trade.buyerId,
                    guildId: trade.guildId,
                    amount: trade.amount,
                    transactionType: 'LOCK',
                    status: 'COMPLETED',
                    timestamp: new Date(),
                    completedAt: new Date(),
                    relatedTransactionId: transactionId,
                    details: `Funds locked in escrow for trade ${trade.tradeId}`,
                    reason: 'Trade escrow lock'
                }], { session });
            // Update trade escrow status
            trade.escrowLocked = true;
            trade.escrowAmount = trade.amount;
            await trade.save({ session });
            this.logOperation('Escrow funds locked successfully', {
                tradeId: trade.tradeId,
                escrowId: escrowTransaction[0].escrowId,
                amount: trade.amount
            });
            return escrowTransaction[0];
        }
        catch (error) {
            this.handleError(error, { operation: 'lock_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    /**
     * Release escrow funds to seller
     */
    async releaseEscrow(trade, reason, session) {
        this.logOperation('Releasing escrow funds', {
            tradeId: trade.tradeId,
            sellerId: trade.sellerId,
            amount: trade.escrowAmount
        });
        try {
            if (!trade.escrowLocked || trade.escrowAmount <= 0) {
                throw new errorHandler_1.ValidationError('No funds in escrow for this trade');
            }
            // Add to seller's balance
            const seller = await models_1.User.findOneAndUpdate({ discordId: trade.sellerId }, { $inc: { balance: trade.escrowAmount } }, { new: true, upsert: true, session });
            if (!seller) {
                throw new errorHandler_1.DatabaseError('Failed to update seller balance');
            }
            // Create main transaction record
            const transactionId = this.generateTransactionId();
            await models_1.Transaction.create([{
                    discordId: trade.sellerId,
                    type: 'trade_release',
                    amount: trade.escrowAmount,
                    details: `Escrow released for trade ${trade.tradeId}`,
                    tradeId: trade.tradeId,
                    timestamp: new Date()
                }], { session });
            // Create escrow transaction record
            const escrowTransaction = await models_1.EscrowTransaction.create([{
                    escrowId: this.generateEscrowId(),
                    tradeId: trade.tradeId,
                    discordId: trade.sellerId,
                    guildId: trade.guildId,
                    amount: trade.escrowAmount,
                    transactionType: 'RELEASE',
                    status: 'COMPLETED',
                    timestamp: new Date(),
                    completedAt: new Date(),
                    relatedTransactionId: transactionId,
                    details: `Escrow funds released to seller for trade ${trade.tradeId}`,
                    reason
                }], { session });
            // Update trade escrow status
            trade.escrowLocked = false;
            await trade.save({ session });
            this.logOperation('Escrow funds released successfully', {
                tradeId: trade.tradeId,
                escrowId: escrowTransaction[0].escrowId,
                amount: trade.escrowAmount,
                sellerId: trade.sellerId
            });
            return escrowTransaction[0];
        }
        catch (error) {
            this.handleError(error, { operation: 'release_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    /**
     * Refund escrow funds to buyer
     */
    async refundEscrow(trade, reason, session) {
        this.logOperation('Refunding escrow funds', {
            tradeId: trade.tradeId,
            buyerId: trade.buyerId,
            amount: trade.escrowAmount
        });
        try {
            if (!trade.escrowLocked || trade.escrowAmount <= 0) {
                throw new errorHandler_1.ValidationError('No funds in escrow for this trade');
            }
            // Add back to buyer's balance
            const buyer = await models_1.User.findOneAndUpdate({ discordId: trade.buyerId }, { $inc: { balance: trade.escrowAmount } }, { new: true, upsert: true, session });
            if (!buyer) {
                throw new errorHandler_1.DatabaseError('Failed to update buyer balance');
            }
            // Create main transaction record
            const transactionId = this.generateTransactionId();
            await models_1.Transaction.create([{
                    discordId: trade.buyerId,
                    type: 'trade_refund',
                    amount: trade.escrowAmount,
                    details: `Escrow refunded for trade ${trade.tradeId}`,
                    tradeId: trade.tradeId,
                    timestamp: new Date()
                }], { session });
            // Create escrow transaction record
            const escrowTransaction = await models_1.EscrowTransaction.create([{
                    escrowId: this.generateEscrowId(),
                    tradeId: trade.tradeId,
                    discordId: trade.buyerId,
                    guildId: trade.guildId,
                    amount: trade.escrowAmount,
                    transactionType: 'REFUND',
                    status: 'COMPLETED',
                    timestamp: new Date(),
                    completedAt: new Date(),
                    relatedTransactionId: transactionId,
                    details: `Escrow funds refunded to buyer for trade ${trade.tradeId}`,
                    reason
                }], { session });
            // Update trade escrow status
            trade.escrowLocked = false;
            await trade.save({ session });
            this.logOperation('Escrow funds refunded successfully', {
                tradeId: trade.tradeId,
                escrowId: escrowTransaction[0].escrowId,
                amount: trade.escrowAmount,
                buyerId: trade.buyerId
            });
            return escrowTransaction[0];
        }
        catch (error) {
            this.handleError(error, { operation: 'refund_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    /**
     * Split escrow funds between parties (for dispute resolution)
     */
    async splitEscrow(trade, sellerAmount, buyerAmount, reason, session) {
        this.logOperation('Splitting escrow funds', {
            tradeId: trade.tradeId,
            sellerAmount,
            buyerAmount,
            totalEscrow: trade.escrowAmount
        });
        try {
            if (!trade.escrowLocked || trade.escrowAmount <= 0) {
                throw new errorHandler_1.ValidationError('No funds in escrow for this trade');
            }
            if (sellerAmount + buyerAmount !== trade.escrowAmount) {
                throw new errorHandler_1.ValidationError('Split amounts must equal total escrow amount');
            }
            if (sellerAmount < 0 || buyerAmount < 0) {
                throw new errorHandler_1.ValidationError('Split amounts cannot be negative');
            }
            let sellerTransaction;
            let buyerTransaction;
            // Release to seller if amount > 0
            if (sellerAmount > 0) {
                const seller = await models_1.User.findOneAndUpdate({ discordId: trade.sellerId }, { $inc: { balance: sellerAmount } }, { new: true, upsert: true, session });
                if (!seller) {
                    throw new errorHandler_1.DatabaseError('Failed to update seller balance');
                }
                // Create seller transaction records
                const sellerTransactionId = this.generateTransactionId();
                await models_1.Transaction.create([{
                        discordId: trade.sellerId,
                        type: 'trade_release',
                        amount: sellerAmount,
                        details: `Partial escrow release (dispute resolution) for trade ${trade.tradeId}`,
                        tradeId: trade.tradeId,
                        timestamp: new Date()
                    }], { session });
                const sellerEscrowTx = await models_1.EscrowTransaction.create([{
                        escrowId: this.generateEscrowId(),
                        tradeId: trade.tradeId,
                        discordId: trade.sellerId,
                        guildId: trade.guildId,
                        amount: sellerAmount,
                        transactionType: 'RELEASE',
                        status: 'COMPLETED',
                        timestamp: new Date(),
                        completedAt: new Date(),
                        relatedTransactionId: sellerTransactionId,
                        details: `Partial escrow release to seller (dispute resolution) for trade ${trade.tradeId}`,
                        reason
                    }], { session });
                sellerTransaction = sellerEscrowTx[0];
            }
            // Refund to buyer if amount > 0
            if (buyerAmount > 0) {
                const buyer = await models_1.User.findOneAndUpdate({ discordId: trade.buyerId }, { $inc: { balance: buyerAmount } }, { new: true, upsert: true, session });
                if (!buyer) {
                    throw new errorHandler_1.DatabaseError('Failed to update buyer balance');
                }
                // Create buyer transaction records
                const buyerTransactionId = this.generateTransactionId();
                await models_1.Transaction.create([{
                        discordId: trade.buyerId,
                        type: 'trade_refund',
                        amount: buyerAmount,
                        details: `Partial escrow refund (dispute resolution) for trade ${trade.tradeId}`,
                        tradeId: trade.tradeId,
                        timestamp: new Date()
                    }], { session });
                const buyerEscrowTx = await models_1.EscrowTransaction.create([{
                        escrowId: this.generateEscrowId(),
                        tradeId: trade.tradeId,
                        discordId: trade.buyerId,
                        guildId: trade.guildId,
                        amount: buyerAmount,
                        transactionType: 'REFUND',
                        status: 'COMPLETED',
                        timestamp: new Date(),
                        completedAt: new Date(),
                        relatedTransactionId: buyerTransactionId,
                        details: `Partial escrow refund to buyer (dispute resolution) for trade ${trade.tradeId}`,
                        reason
                    }], { session });
                buyerTransaction = buyerEscrowTx[0];
            }
            // Update trade escrow status
            trade.escrowLocked = false;
            await trade.save({ session });
            this.logOperation('Escrow funds split successfully', {
                tradeId: trade.tradeId,
                sellerAmount,
                buyerAmount
            });
            return { sellerTransaction: sellerTransaction, buyerTransaction };
        }
        catch (error) {
            this.handleError(error, { operation: 'split_escrow', tradeId: trade.tradeId });
            throw error;
        }
    }
    /**
     * Get escrow balance for a trade
     */
    async getEscrowBalance(tradeId) {
        try {
            return await models_1.EscrowTransaction.getEscrowBalance(tradeId);
        }
        catch (error) {
            this.handleError(error, { operation: 'get_escrow_balance', tradeId });
            throw error;
        }
    }
    /**
     * Get user's total escrowed amount
     */
    async getUserEscrowedAmount(discordId) {
        try {
            return await models_1.EscrowTransaction.getUserEscrowedAmount(discordId);
        }
        catch (error) {
            this.handleError(error, { operation: 'get_user_escrowed_amount', discordId });
            throw error;
        }
    }
    // Private helper methods
    generateEscrowId() {
        return `escrow_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    generateTransactionId() {
        return `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
exports.EscrowManager = EscrowManager;
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "lockEscrow", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "releaseEscrow", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "refundEscrow", null);
__decorate([
    (0, decorators_1.requireFeature)('TRADE_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, String, Object]),
    __metadata("design:returntype", Promise)
], EscrowManager.prototype, "splitEscrow", null);
//# sourceMappingURL=EscrowManager.js.map